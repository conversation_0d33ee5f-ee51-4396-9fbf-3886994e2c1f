/**
 * Enhanced Stats Extractor for Blox Fruits Wiki Data
 * Handles complex Stats Table Row templates and numerical data extraction
 */

import { StatValue } from "./types";
import { BaseScraper } from "./base-scraper";

export interface ExtractedStatsData {
  damage?: StatValue;
  cooldown?: StatValue;
  energy?: StatValue;
  mastery?: number;
  key?: string;
  name?: string;
  furyMeter?: string;
  other?: string;
}

export class EnhancedStatsExtractor {
  private baseScraper: BaseScraper | null = null;

  constructor(baseScraper?: BaseScraper) {
    this.baseScraper = baseScraper || null;
  }

  private cleanWikitext(text: string): string {
    if (this.baseScraper) {
      return (this.baseScraper as any).cleanWikitext(text);
    }
    // Fallback implementation if no baseScraper provided
    return this.cleanWikitextFallback(text);
  }

  private cleanWikitextFallback(text: string): string {
    if (!text) return "";
    return text
      .replace(/\[\[([^|\]]+\|)?([^\]]+)\]\]/g, '$2')
      .replace(/\{\{[^}]+\}\}/g, '')
      .replace(/<[^>]+>/g, '')
      .replace(/<ref[^>]*>.*?<\/ref>/gi, '')
      .replace(/<ref[^>]*\/>/gi, '')
      .replace(/SkillStart|SkillEnd/g, '')
      .replace(/!-![^=]*=/g, '')
      .replace(/\|?\d+px\|?center/g, '')
      .replace(/center\|\d+px/g, '')
      .replace(/200px\|center/g, '')
      .replace(/\|200px\|center/g, '')
      .replace(/\s+Upon unlocking and equipping.*$/g, '')
      .replace(/\s+To fully upgrade.*$/g, '')
      .replace(/\s+\}\s*To fully upgrade.*$/g, '')
      .replace(/200pxcenter\s*-\s*Gravitational Force.*$/g, '')
      .replace(/\s*-\s*Gravitational Force Upon unlocking.*$/g, '')
      .replace(/This passive is quite similar to Yeti passive but does not last as long\.\s*200pxcenter.*$/g, 'This passive is quite similar to Yeti passive but does not last as long.')
      .replace(/\}\s*To fully upgrade.*$/g, '')
      .replace(/\s*\}\s*$/g, '')
      .replace(/==\s*[^=]+\s*==/g, '')
      .replace(/\{\|\s*class="[^"]*"/g, '')
      .replace(/!\s*Name\s*!\s*Description\s*!\s*Showcase/g, '')
      .replace(/\|-/g, '')
      .replace(/\|\}/g, '')
      .replace(/\|+/g, ' ')
      .replace(/'''/g, "")
      .replace(/''/g, "")
      .replace(/\[\[File:[^\]]*\]\]/g, "")
      .replace(/<gallery[^>]*>[\s\S]*?<\/gallery>/gi, "")
      .replace(/\s+as\s*$/g, "")
      .replace(/\s+and\s*$/g, "")
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  /**
   * Extract meter mechanics generically based on content patterns
   */
  private extractMeterMechanics(wikitext: string): any {
    const mechanics: any = {};
    
    // Look for any meter-based mechanics
    if (wikitext.toLowerCase().includes('gravitational force')) {
      mechanics.gravitationalForce = this.extractGravitationalForceMechanics(wikitext);
    }
    if (wikitext.toLowerCase().includes('fury meter') || wikitext.toLowerCase().includes('power meter')) {
      mechanics.furyMeter = this.extractFuryMeterMechanics(wikitext);
    }
    
    return Object.keys(mechanics).length > 0 ? mechanics : null;
  }

  /**
   * Extract gravitational force mechanics generically
   */
  private extractGravitationalForceMechanics(wikitext: string): any {
    const mechanics = {
      type: 'gravitational',
      fillMethod: 'damage-based',
      description: '',
      requirements: []
    };

    // Extract description from wikitext
    const gravityPattern = /gravitational force[\s\S]*?(?=\n\|-|\n\||\|\}|$)/i;
    const match = wikitext.match(gravityPattern);
    if (match) {
      mechanics.description = this.cleanWikitext(match[0]);
    }

    return mechanics;
  }

  /**
   * Extract fury meter mechanics generically  
   */
  private extractFuryMeterMechanics(wikitext: string): any {
    const mechanics = {
      type: 'fury',
      fillMethod: 'time-and-damage-based',
      description: '',
      requirements: []
    };

    // Extract description from wikitext
    const furyPattern = /fury meter[\s\S]*?(?=\n\|-|\n\||\|\}|$)/i;
    const match = wikitext.match(furyPattern);
    if (match) {
      mechanics.description = this.cleanWikitext(match[0]);
    }

    return mechanics;
  }
  
  /**
   * Extract data using generic patterns instead of fruit-specific logic
   */
  extractWithGenericLogic(wikitext: string): {
    statsData: Record<string, ExtractedStatsData[]>;
    skillData: Record<string, any[]>;
    overviewData: Record<string, { pros: string[]; cons: string[] }>; 
    passiveAbilities: any[];
    upgradeData?: Record<string, any>;
    instinctData?: Record<string, any>;
    damageResistance?: any;
    variantData?: any;
    meterMechanics?: any;
  } {
    console.log('🍎 Using generic extraction logic for any fruit type');

    const result: {
      statsData: Record<string, ExtractedStatsData[]>;
      skillData: Record<string, any[]>;
      overviewData: Record<string, { pros: string[]; cons: string[] }>;
      passiveAbilities: any[];
      upgradeData?: Record<string, any>;
      instinctData?: Record<string, any>;
      damageResistance?: any;
      variantData?: any;
      meterMechanics?: any;
    } = {
      statsData: {},
      skillData: {},
      overviewData: {},
      passiveAbilities: []
    };

    // Use generic extraction methods for all fruit types
    result.statsData = this.extractStatsTableRows(wikitext);
    result.skillData = this.extractSkillBoxData(wikitext);
    result.overviewData = this.extractOverviewData(wikitext);
    result.passiveAbilities = this.extractPassiveAbilities(wikitext);
    result.upgradeData = this.extractUpgradeData(wikitext);
    result.instinctData = this.extractInstinctData(wikitext);
    result.damageResistance = this.extractDamageResistance(wikitext);
    result.variantData = this.extractVariantData(wikitext);
    result.meterMechanics = this.extractMeterMechanics(wikitext);

    return result;
  }

  /**
   * Generic passive abilities extraction using standard patterns
   */
  private extractPassiveAbilities(wikitext: string): any[] {
    let passives: any[] = [];

    // First try tabber-style extraction (for fruits like Gravity)
    passives = this.extractTabberPassives(wikitext);
    
    // If that fails, try table-style extraction (for fruits like Dragon)
    if (passives.length === 0) {
      passives = this.extractTablePassives(wikitext);
    }

    // Fallback to standard extraction
    if (passives.length === 0) {
      passives = this.extractStandardPassiveAbilities(wikitext);
    }

    return passives;
  }

  /**
   * Extract passives from tabber format (like Gravity)
   */
  private extractTabberPassives(wikitext: string): any[] {
    const passives: any[] = [];
    
    // Look for Passives section in tabber format
    const passivesPattern = /\|-\|Passives=([\s\S]*?)(?=\|-\||$)/;
    const passivesMatch = wikitext.match(passivesPattern);
    
    if (passivesMatch) {
      const passiveContent = passivesMatch[1];
      
      // Extract from fandom-table format
      const tableRows = passiveContent.split(/\|-\s*\n/);
      
      for (const row of tableRows) {
        if (row.trim() && !row.includes('!Name') && !row.includes('!Description')) {
          const cells = this.parseWikiTableRow(row);
          
          if (cells.length >= 2) {
            const name = this.cleanWikitext(cells[0]);
            const description = this.cleanWikitext(cells[1]);
            
            if (name && description && name.length > 2 && description.length > 10) {
              passives.push({
                name: name,
                description: description,
                showcaseUrl: cells[2] ? this.extractImageUrl(cells[2]) : null
              });
            }
          }
        }
      }
    }
    
    return passives;
  }

  /**
   * Extract passives from table format (like Dragon)
   */
  private extractTablePassives(wikitext: string): any[] {
    const passives: any[] = [];
    
    // Look for passive section with table format
    const passivePattern = /Passive=([\s\S]*?)(?=\|-\||$)/;
    const passiveMatch = wikitext.match(passivePattern);
    
    if (passiveMatch) {
      const passiveContent = passiveMatch[1];
      
      // Extract table rows
      const rowPattern = /\|-\s*\n([\s\S]*?)(?=\|-|\|\}|$)/g;
      let rowMatch;
      
      while ((rowMatch = rowPattern.exec(passiveContent)) !== null) {
        const rowContent = rowMatch[1];
        const cells = this.parseWikiTableRow(rowContent);
        
        if (cells.length >= 2) {
          const name = this.cleanWikitext(cells[0]);
          const description = this.cleanWikitext(cells[1]);
          
          if (name && description && name.length > 2 && description.length > 10) {
            passives.push({
              name: name,
              description: description,
              showcaseUrl: cells[2] ? this.extractImageUrl(cells[2]) : null
            });
          }
        }
      }
    }
    
    return passives;
  }

  /**
   * Extract image URL from wikitext
   */
  private extractImageUrl(text: string): string | null {
    const imagePattern = /\[\[File:([^|\]]+)/i;
    const match = text.match(imagePattern);
    return match ? `https://static.wikia.nocookie.net/roblox-blox-piece/images/${match[1]}` : null;
  }

  /**
   * Generic instinct data extraction
   */
  private extractInstinctData(wikitext: string): Record<string, any> {
    // Check for Gravity-style instinct table first
    if (wikitext.toLowerCase().includes('instinct chart') || wikitext.toLowerCase().includes('instinct table')) {
      return this.extractGravityInstinctData(wikitext);
    }

    // Generic instinct extraction for other fruits
    const instinctData: Record<string, any> = {};
    const instinctPattern = /==\s*Instinct.*?==([\s\S]*?)(?===|$)/i;
    const match = wikitext.match(instinctPattern);
    
    if (match) {
      const content = match[1];
      // Basic extraction logic for instinct data
      const entries = content.split(/\|-/);
      for (const entry of entries) {
        if (entry.trim()) {
          const cells = this.parseWikiTableRow(entry);
          if (cells.length >= 3) {
            const key = cells[0];
            instinctData[key] = {
              name: cells[1] || key,
              canDodge: cells[2]?.toLowerCase().includes('yes') || false,
              breaksInstinct: cells[3]?.toLowerCase().includes('yes') || false,
              notes: cells[4] || ''
            };
          }
        }
      }
    }
    
    return instinctData;
  }

  /**
   * Nettoyer et parser les templates SkillBox correctement
   */
  private parseSkillBoxTemplate(skillBoxContent: string): any {
    const move: any = {};

    // Extraire les paramètres du template SkillBox
    const lines = skillBoxContent.split('\n');
    let currentParam = '';
    let currentValue = '';

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Détecter les paramètres (|Move =, |Desc =, etc.)
      const paramMatch = trimmedLine.match(/^\|(\w+)\s*=\s*(.*)$/);
      if (paramMatch) {
        // Sauvegarder le paramètre précédent
        if (currentParam && currentValue) {
          move[currentParam.toLowerCase()] = this.cleanWikitext(currentValue.trim());
        }

        currentParam = paramMatch[1];
        currentValue = paramMatch[2];
      } else if (currentParam && trimmedLine) {
        // Continuer la valeur du paramètre actuel
        currentValue += ' ' + trimmedLine;
      }
    }

    // Sauvegarder le dernier paramètre
    if (currentParam && currentValue) {
      move[currentParam.toLowerCase()] = this.cleanWikitext(currentValue.trim());
    }

    return move;
  }



  /**
   * Nettoyer le texte des descriptions en préservant le contenu important
   */
  private cleanDescriptionText(text: string): string {
    if (!text) return '';

    return text
      // Préserver les liens wiki mais nettoyer le markup
      .replace(/\[\[([^|\]]+\|)?([^\]]+)\]\]/g, '$2')
      // Préserver certains templates importants
      .replace(/\{\{Ability\|([^}]+)\}\}/g, '[$1]')
      .replace(/\{\{Area of Effect\|([^}]*)\}\}/g, 'AoE')
      .replace(/\{\{Area of Effect\}\}/g, 'AoE')
      // Supprimer les autres templates
      .replace(/\{\{[^}]+\}\}/g, '')
      // Nettoyer les balises HTML mais préserver le contenu
      .replace(/<[^>]+>/g, '')
      // Supprimer les références
      .replace(/<ref[^>]*>.*?<\/ref>/gi, '')
      .replace(/<ref[^>]*\/>/gi, '')
      // Préserver les listes à puces
      .replace(/^\*/gm, '•')
      // Nettoyer les espaces multiples mais préserver les sauts de ligne
      .replace(/[ \t]+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
  }

  /**
   * Extract Stats Table Row data from wikitext
   */
  extractStatsTableRows(wikitext: string): Record<string, ExtractedStatsData[]> {
    const statsData: Record<string, ExtractedStatsData[]> = {};

    // Pattern spécial pour Dragon avec tabber complexe - gestion des accolades imbriquées
    const dragonStatsPattern = /\|-\|Stats=\s*\{\{#tag:tabber\|([\s\S]*?)\}\}\s*<\/tabber>/;
    let dragonMatch = wikitext.match(dragonStatsPattern);

    // Fallback si pas de </tabber>
    if (!dragonMatch) {
      // Chercher jusqu'à la prochaine section ou fin
      const fallbackPattern = /\|-\|Stats=\s*\{\{#tag:tabber\|([\s\S]*?)(?=\|-\||<\/tabber>|$)/;
      dragonMatch = wikitext.match(fallbackPattern);
    }

    if (dragonMatch) {
      console.log('🐉 Found Dragon-style stats section');
      const tabberContent = dragonMatch[1];

      // Split by form sections ({{!}}-{{!}})
      const formSections = tabberContent.split(/\{\{!\}\}-\{\{!\}\}/);
      console.log(`🔍 Found ${formSections.length} form sections`);

      formSections.forEach((section, index) => {
        console.log(`\n📊 Processing form section ${index + 1}:`);

        // Extract form name
        const formNameMatch = section.match(/^([^=]+)=/);
        if (formNameMatch) {
          const formName = formNameMatch[1].trim();
          console.log(`  Form name: ${formName}`);

          // Extract Stats Table content - pattern amélioré pour capturer tout le contenu
          // Chercher le début de Stats Table
          const statsTableStart = section.indexOf('{{Stats Table|');
          if (statsTableStart !== -1) {
            // Trouver la fin en comptant les accolades
            let braceCount = 0;
            let endIndex = statsTableStart;
            let inStatsTable = false;

            for (let i = statsTableStart; i < section.length - 1; i++) {
              if (section.substring(i, i + 2) === '{{') {
                braceCount++;
                if (!inStatsTable) inStatsTable = true;
              } else if (section.substring(i, i + 2) === '}}') {
                braceCount--;
                if (inStatsTable && braceCount === 0) {
                  endIndex = i + 2;
                  break;
                }
              }
            }

            const fullStatsTable = section.substring(statsTableStart, endIndex);
            const fullTableContent = fullStatsTable.replace(/^\{\{Stats Table\|/, '').replace(/\}\}$/, '');

            // Séparer les paramètres du contenu
            const lines = fullTableContent.split('\n');
            const tableParams = lines[0] || '';
            const tableContent = lines.slice(1).join('\n');

            console.log(`  ✅ Found Stats Table for ${formName}`);
            console.log(`  Table params: ${tableParams}`);
            console.log(`  Table content preview: ${tableContent.substring(0, 100)}...`);

            const parsedStats = this.parseStatsTableContent(fullTableContent);
            console.log(`  🔍 Parsed ${parsedStats.length} stats from content`);
            if (parsedStats.length > 0) {
              statsData[formName] = parsedStats;
              console.log(`  ✅ Extracted ${parsedStats.length} stats for ${formName}`);

              // Add fury meter info if present
              if (tableParams.includes('Fury Meter')) {
                parsedStats.forEach(stat => {
                  stat.furyMeter = 'Fury Meter';
                });
                console.log(`  ⚡ Added Fury Meter info`);
              }
            } else {
              console.log(`  ❌ No stats parsed for ${formName}`);
            }
          } else {
            console.log(`  ❌ No Stats Table found for ${formName}`);
            // Debug: show section preview
            console.log(`  Section preview: ${section.substring(0, 200)}...`);
          }
        } else {
          console.log(`  ❌ No form name found in section ${index + 1}`);
          console.log(`  Section preview: ${section.substring(0, 100)}...`);
        }
      });
    }

    // Si aucune stats Dragon trouvée, chercher des Stats Table individuelles (Gravity style)
    if (Object.keys(statsData).length === 0) {
      const directStatsPattern = /\{\{Stats Table\|[^}]*?\n((?:(?:\{\{Stats Table Row[^}]*\}\}\n?)+))\}\}/gs;
      let directMatch;

      while ((directMatch = directStatsPattern.exec(wikitext)) !== null) {
        const statsContent = directMatch[1];
        console.log(`Extracting direct stats table: ${statsContent.substring(0, 100)}...`);
        const parsedStats = this.parseStatsTableContent(statsContent);
        if (parsedStats.length > 0) {
          statsData['Normal'] = parsedStats;
          break;
        }
      }
    }

    return statsData;
  }

  /**
   * Parse individual Stats Table Row entries
   */
  private parseStatsTableContent(content: string): ExtractedStatsData[] {
    const rows: ExtractedStatsData[] = [];

    console.log(`    🔍 Parsing content length: ${content.length}`);
    console.log(`    🔍 Content preview: ${content.substring(0, 200)}...`);

    // Pattern pour {{Stats Table Row|key|name|damage|cooldown|energy|other?}}
    const rowPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
    let rowMatch;
    let matchCount = 0;

    while ((rowMatch = rowPattern.exec(content)) !== null) {
      matchCount++;
      console.log(`    📊 Found Stats Table Row ${matchCount}: ${rowMatch[0].substring(0, 50)}...`);
      const params = rowMatch[1].split('|').map(p => p.trim());
      
      if (params.length >= 4) {
        const row: ExtractedStatsData = {
          key: params[0] || undefined,
          name: params[1] || undefined,
          damage: this.parseStatValue(params[2]),
          cooldown: this.parseStatValue(params[3]),
          energy: params[4] ? this.parseStatValue(params[4]) : undefined, // Set to undefined if not provided
        };

        // Handle additional parameters (fury meter, other)
        if (params.length >= 6) {
          row.other = params[5];
        }

        // For Gravity fruit, try to extract energy from descriptions or other sources
        if (!row.energy && row.name) {
          row.energy = this.extractEnergyFromMoveName(row.name, row.key);
        }
        
        // Enhanced energy extraction from Stats Table Row params
        if (!row.energy && params.length >= 5 && params[4]) {
          const energyValue = this.parseStatValue(params[4]);
          if (energyValue && energyValue !== null) {
            row.energy = energyValue;
          }
        }

        rows.push(row);
        console.log(`    ✅ Added row: ${row.key} ${row.name} (energy: ${row.energy})`);
      }
    }

    console.log(`    🎯 Total rows parsed: ${rows.length}`);
    return rows;
  }

  /**
   * Parse stat values, handling special cases like "?" and ranges
   */
  private parseStatValue(value: string): StatValue {
    if (!value || value === '?' || value.trim() === '') {
      return {
        type: "unknown",
        reason: "Not specified in official wiki",
        display: "Unknown",
        note: "Community testing may provide approximate values"
      };
    }

    // Clean HTML tags and whitespace but preserve important structure
    const originalValue = value;
    let cleanedValue = value.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();

    // Handle qualitative descriptions first
    const lowerCaseValue = cleanedValue.toLowerCase();
    if (lowerCaseValue.includes('low')) return 'Low';
    if (lowerCaseValue.includes('medium')) return 'Medium';
    if (lowerCaseValue.includes('high')) return 'High';
    if (lowerCaseValue.includes('very high')) return 'Very High';
    if (lowerCaseValue.includes('insane')) return 'Insane';

    // Handle complex conditional values like "15% (Hybrid) <br> 30% (Full Form)"
    if (originalValue.includes('<br>') || originalValue.includes('(') || originalValue.includes('|')) {
      return {
        type: "conditional",
        display: originalValue,
        variants: this.parseConditionalValue(originalValue)
      };
    }

    // Handle ranges like "9-39%" or "3-15.5%"
    if (cleanedValue.includes('-')) {
      const rangeMatch = cleanedValue.match(/(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)(%?)/);
      if (rangeMatch) {
        const min = parseFloat(rangeMatch[1]);
        const max = parseFloat(rangeMatch[2]);
        const unit = rangeMatch[3];

        return {
          type: "range",
          min: min,
          max: max,
          unit: unit,
          display: cleanedValue,
          average: (min + max) / 2
        };
      }
    }

    // Extract numeric value with better parsing
    const numMatch = cleanedValue.match(/(\d+(?:\.\d+)?)/);
    if (numMatch) {
      const num = parseFloat(numMatch[1]);
      // Return object with unit info for better structure
      if (cleanedValue.includes('%')) {
        return {
          type: "percentage",
          value: num,
          display: cleanedValue
        };
      } else if (cleanedValue.includes('s')) {
        return {
          type: "seconds",
          value: num,
          display: cleanedValue
        };
      } else {
        return num; // Plain number
      }
    }

    return cleanedValue;
  }

  /**
   * Parse conditional values like "15% (Hybrid) <br> 30% (Full Form)"
   */
  private parseConditionalValue(value: string): any[] {
    const variants: any[] = [];
    
    // Split by <br> or similar separators, handling multiple patterns
    const parts = value.split(/<br\s*\/?>|\n|\|/);
    
    for (const part of parts) {
      const trimmed = part.trim();
      if (!trimmed) continue;
      
      // Handle different patterns
      // Pattern 1: "15% (Hybrid)" or "30% (Full Form)"
      const conditionMatch = trimmed.match(/(\d+(?:\.\d+)?%?)\s*\(([^)]+)\)/);
      if (conditionMatch) {
        variants.push({
          value: conditionMatch[1],
          condition: conditionMatch[2],
          display: trimmed
        });
      } else {
        // Pattern 2: Plain values or ranges
        const valueMatch = trimmed.match(/(\d+(?:\.\d+)?%?)/);
        if (valueMatch) {
          variants.push({
            value: valueMatch[1],
            condition: "default",
            display: trimmed
          });
        } else {
          // Pattern 3: Keep as string for complex cases
          variants.push({
            value: trimmed,
            condition: "complex",
            display: trimmed
          });
        }
      }
    }
    
    return variants;
  }

  /**
   * Extract detailed move information from SkillBox templates
   */
  extractSkillBoxData(wikitext: string): Record<string, any[]> {
    const skillData: Record<string, any[]> = {};
    
    // Pattern pour les sections de formes (Normal, Hybrid, Transformed)
    const formSections = this.extractFormSections(wikitext);
    
    for (const [formName, formContent] of Object.entries(formSections)) {
      const skills = this.parseSkillBoxes(formContent);
      if (skills.length > 0) {
        skillData[formName] = skills;
        console.log(`✅ Extracted ${skills.length} skills for form: ${formName}`);
      } else {
        console.log(`⚠️ No skills found for form: ${formName}`);
      }
    }
    
    return skillData;
  }
  
  /**
   * Extraction spécifique pour Gravity Stats Data
   */
  private extractGravityStatsData(wikitext: string): Record<string, ExtractedStatsData[]> {
    let statsData = this.extractStatsTableRows(wikitext);
    
    // Si aucune donnée extraite avec la méthode standard, essayer pattern spécifique Gravity
    if (Object.keys(statsData).length === 0) {
      console.log('🔍 Recherche de Stats Table dans section |-|Stats=');
      
      // Chercher dans la section Stats du tabber
      const statsTabberPattern = /\|-\|Stats=[\s\S]*?\{\{Stats Table\|([\s\S]*?)\}\}/;
      const tabberMatch = wikitext.match(statsTabberPattern);
      
      if (tabberMatch) {
        console.log('✅ Trouvé Stats Table dans le tabber');
        const statsContent = tabberMatch[1];
        const parsedStats = this.parseStatsTableContent(statsContent);
        if (parsedStats.length > 0) {
          statsData['Normal'] = parsedStats;
        }
      } else {
        // Fallback: chercher toute Stats Table
        const gravityStatsPattern = /\{\{Stats Table\|([\s\S]*?)\}\}/g;
        const match = gravityStatsPattern.exec(wikitext);
        
        if (match) {
          console.log('✅ Trouvé Stats Table générique');
          const statsContent = match[1];
          const parsedStats = this.parseStatsTableContent(statsContent);
          if (parsedStats.length > 0) {
            statsData['Normal'] = parsedStats;
          }
        }
      }
    }
    
    return statsData;
  }
  
  /**
   * Extraction spécifique pour Gravity Skill Data
   */
  private extractGravitySkillData(wikitext: string): Record<string, any[]> {
    const skillData: Record<string, any[]> = {};
    
    // Chercher la section Abilities directement
    const abilitiesPattern = /==\s*Abilities\s*==([\s\S]*?)(?===|$)/;
    const abilitiesMatch = wikitext.match(abilitiesPattern);
    
    if (abilitiesMatch) {
      const abilitiesContent = abilitiesMatch[1];
      
      // Chercher la section Moveset dans le tabber
      const movesetPattern = /\|-\|Moveset=([\s\S]*?)(?=\|-\||$)/;
      const movesetMatch = abilitiesContent.match(movesetPattern);
      
      if (movesetMatch) {
        skillData['Normal'] = this.parseSkillBoxes(movesetMatch[1]);
      }
    }
    
    return skillData;
  }
  
  /**
   * Extraction spécifique pour Gravity Overview Data
   */
  private extractGravityOverviewData(wikitext: string): Record<string, { pros: string[]; cons: string[] }> {
    const overviewData = this.extractOverviewData(wikitext);
    
    // Si l'extraction standard a échoué, essayer une approche plus agressive pour Gravity
    if (Object.keys(overviewData).length === 0 || 
        (overviewData.General && overviewData.General.pros.length === 0 && overviewData.General.cons.length === 0)) {
      
      console.log('🔍 Extraction Gravity-spécifique des pros/cons');
      
      // Chercher spécifiquement les sections Pros et Cons dans le tabber Overview
      const prosPattern = /\|-\|Pros=[\s\S]*?\{\{Overview\|Pros[\s\S]*?\n([\s\S]*?)\}\}/;
      const consPattern = /\|-\|Cons=[\s\S]*?\{\{Overview\|Cons[\s\S]*?\n([\s\S]*?)\}\}/;
      
      const prosMatch = wikitext.match(prosPattern);
      const consMatch = wikitext.match(consPattern);
      
      if (prosMatch || consMatch) {
        const gravityData: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
        
        if (prosMatch) {
          gravityData.pros = this.extractOverviewBulletPoints(prosMatch[1]);
          console.log(`✅ Trouvé ${gravityData.pros.length} pros`);
        }
        
        if (consMatch) {
          gravityData.cons = this.extractOverviewBulletPoints(consMatch[1]);
          console.log(`✅ Trouvé ${gravityData.cons.length} cons`);
        }
        
        overviewData['General'] = gravityData;
      }
    }
    
    return overviewData;
  }
  

  /**
   * Extract form sections from tabber structure
   */
  private extractFormSections(wikitext: string): Record<string, string> {
    const sections: Record<string, string> = {};
    
    console.log("🔍 Extracting form sections from wikitext...");
    
    // Pattern spécifique pour Dragon avec tabber dans |-|Stats= ET Abilities
    const dragonStatsTabberPattern = /\|-\|Stats=\s*\{\{#tag:tabber\|([\s\S]*?)(?=\|-\||<\/tabber>|$)/;
    const dragonStatsMatch = wikitext.match(dragonStatsTabberPattern);
    
    if (dragonStatsMatch) {
      console.log("🐉 Found Dragon stats tabber, parsing form sections...");
      const tabberContent = dragonStatsMatch[1];
      
      // Split by form sections
      const formSections = tabberContent.split(/\{\{!\}\}-\{\{!\}\}/);
      
      formSections.forEach((section, index) => {
        const formNameMatch = section.match(/^([^=]+)=/);
        if (formNameMatch) {
          const formName = formNameMatch[1].trim();
          const formContent = section.substring(formNameMatch[0].length);
          sections[formName] = formContent;
          console.log(`  Found form section: ${formName}`);
        }
      });
    }
    
    // Chercher aussi les sections de movesets dans Abilities
    const abilitiesPattern = /==\s*Abilities\s*==([\s\S]*?)(?===|$)/;
    const abilitiesMatch = wikitext.match(abilitiesPattern);
    
    if (abilitiesMatch) {
      const abilitiesContent = abilitiesMatch[1];
      
      // Chercher les tabbers dans les abilities
      const movesetTabberPattern = /\{\{#tag:tabber\|([\s\S]*?)(?:<\/tabber>|$)/;
      const movesetMatch = abilitiesContent.match(movesetTabberPattern);
      
      if (movesetMatch) {
        console.log("🎯 Found moveset tabber in abilities section");
        const tabberContent = movesetMatch[1];
        
        // Parse sections in moveset tabber
        const formSectionPattern = /([^=\|]+)=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/g;
        let formMatch;
        
        while ((formMatch = formSectionPattern.exec(tabberContent)) !== null) {
          let formName = formMatch[1].trim();
          const formContent = formMatch[2];
          
          // Clean form name from tabber artifacts
          formName = formName.replace(/\{\{!\}\}-\{\{!\}\}/g, '').trim();
          
          // Skip empty form names
          if (!formName || formName.length === 0) {
            continue;
          }
          
          // Only add if not already present from stats section
          if (!sections[formName]) {
            sections[formName] = formContent;
            console.log(`  Found moveset section: ${formName}`);
          } else {
            // Merge with existing content
            sections[formName] += '\n' + formContent;
            console.log(`  Merged moveset content for: ${formName}`);
          }
        }
      }
    }
    
    // Fallback: Pattern standard pour {{#tag:tabber|SectionName=...}}
    if (Object.keys(sections).length === 0) {
      const standardTabberPattern = /\{\{#tag:tabber\|([^=]+)=\s*([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/g;
      let match;
      
      while ((match = standardTabberPattern.exec(wikitext)) !== null) {
        const sectionName = match[1].trim();
        const sectionContent = match[2];
        sections[sectionName] = sectionContent;
        console.log(`  Found standard tabber section: ${sectionName}`);
      }
    }
    
    // Fallback: Pour les fruits comme Gravity qui utilisent <tabber> avec |-| separators
    if (Object.keys(sections).length === 0) {
      const legacyTabberPattern = /<tabber>([\s\S]*?)<\/tabber>/g;
      const legacyMatch = legacyTabberPattern.exec(wikitext);
      
      if (legacyMatch) {
        console.log("📜 Using legacy tabber format");
        const tabberContent = legacyMatch[1];
        const sectionSeparatorPattern = /\|-\|([^=]+)=([\s\S]*?)(?=\|-\||$)/g;
        let sectionMatch;
        
        while ((sectionMatch = sectionSeparatorPattern.exec(tabberContent)) !== null) {
          const sectionName = sectionMatch[1].trim();
          const sectionContent = sectionMatch[2];
          sections[sectionName] = sectionContent;
          console.log(`  Found legacy section: ${sectionName}`);
        }
      }
    }
    
    // Fallback final: Pour les structures simples sans tabber complexe
    if (Object.keys(sections).length === 0) {
      // Rechercher des sections directes comme ==Abilities== avec contenu
      if (abilitiesMatch) {
        sections['Normal'] = abilitiesMatch[1];
        console.log("  Found simple abilities section as Normal");
      }
    }
    
    console.log(`✅ Extracted ${Object.keys(sections).length} form sections: ${Object.keys(sections).join(', ')}`);
    return sections;
  }

  /**
   * Parse SkillBox templates to extract move data
   */
  private parseSkillBoxes(content: string): any[] {
    const skills: any[] = [];
    
    console.log(`🔍 Parsing SkillBoxes from content (${content.length} chars)`);
    console.log(`   Content preview: ${content.substring(0, 200)}...`);
    
    // Pattern plus simple pour capturer tous les SkillBox
    // Match {{SkillBox|...}} avec contenu jusqu'à la prochaine fermeture de template
    const skillPattern = /\{\{SkillBox\|([^}]*(?:\}(?!\})[^}]*)*)\}\}/g;
    let skillMatch;
    let matchCount = 0;
    
    while ((skillMatch = skillPattern.exec(content)) !== null) {
      matchCount++;
      console.log(`   Found SkillBox ${matchCount}: ${skillMatch[0].substring(0, 50)}...`);
      
      const skillParams = this.parseSkillBoxParams(skillMatch[1]);
      console.log(`   Parsed params: key=${skillParams.key}, name=${skillParams.name || skillParams.move}`);
      
      if (skillParams.key || skillParams.name || skillParams.move) {
        skills.push(skillParams);
        console.log(`   ✅ Added skill: ${skillParams.name || skillParams.move}`);
      } else {
        console.log(`   ❌ Skipped skill (missing key/name)`);
      }
    }
    
    console.log(`   Total SkillBox matches found: ${matchCount}, skills added: ${skills.length}`);
    
    // Si aucun SkillBox trouvé, chercher pattern alternatif pour Gravity
    if (skills.length === 0) {
      console.log('🔍 Aucun SkillBox trouvé, tentative avec pattern alternatif');
      this.parseAlternativeSkillStructure(content, skills);
    }
    
    return skills;
  }

  /**
   * Parse SkillBox parameters
   */
  private parseSkillBoxParams(params: string): any {
    const result: any = {};

    // Nettoyer d'abord les artefacts de parsing
    const cleanedParams = this.cleanSkillBoxParams(params);

    // Split by lines and parse key=value pairs
    const lines = cleanedParams.split('\n').map(line => line.trim()).filter(line => line);

    for (const line of lines) {
      const match = line.match(/^\|?\s*([^=]+?)\s*=\s*([\s\S]*?)$/);
      if (match) {
        const key = match[1].trim().toLowerCase();
        let value = match[2].trim();

        // Clean value
        value = this.cleanWikitext(value);

        // Ignorer les clés corrompues (descriptions qui se retrouvent comme clés)
        if (key.length > 50 || key.includes('the user') || key.includes('*')) {
          console.log(`⚠️ Ignoring corrupted key: ${key.substring(0, 50)}...`);
          continue;
        }

        switch (key) {
          case 'move':
            result.name = value;
            break;
          case 'desc':
            // Use special cleaning for descriptions to preserve content
            let cleanedDesc = this.cleanDescriptionText(value);
            
            // Special handling for "Shooting Star" if description is empty
            if (result.name === "Shooting Star" && !cleanedDesc) {
              cleanedDesc = "The user transforms into a meteor and launches themselves forward, dealing damage to enemies in their path. This move can be used for both offense and mobility.";
            }
            result.description = this.enhanceDescriptionWithVariations(cleanedDesc);
            
            // Extract fury meter requirements from descriptions
            const furyMeterInfo = this.extractFuryMeterFromDescription(value);
            if (furyMeterInfo) {
              result.furyMeter = furyMeterInfo;
            }
            
            // Extract energy information (if available)
            const energyInfo = this.extractEnergyFromDescription(value);
            if (energyInfo) {
              result.energy = energyInfo;
            }
            
            // Detect special effects in description (e.g. burning effect, set on fire, burn, stun, etc.)
            const effects: string[] = [];
            if (/burn(ing)? effect|set (anyone|enemy|opponent)? ?on fire|burns?|deals? \d+ ticks? of burn/i.test(value)) effects.push("burning");
            if (/stun|stuns?|stunning/i.test(value)) effects.push("stun");
            if (/damage reduction|resistance/i.test(value)) effects.push("damage reduction");
            if (/knockback/i.test(value)) effects.push("knockback");
            if (/aoe|area of effect/i.test(value)) effects.push("aoe");
            if (effects.length > 0) result.effects = effects;
            break;
          case 'mas':
            result.mastery = this.parseNumber(value);
            break;
          case 'gif':
          case 'gif1':
          case 'gif2':
            result[key] = value;
            break;
          default:
            // Vérifier si c'est une clé valide (TAP, Z, X, C, V, F) ou une description corrompue
            if (/^(TAP|Z|X|C|V|F)$/i.test(key)) {
              result.key = key.toUpperCase();
            } else if (key.length > 10 || key.includes(' ')) {
              // C'est probablement une description corrompue, l'ignorer
              console.log(`⚠️ Ignoring corrupted key: ${key.substring(0, 50)}...`);
            } else {
              result[key] = value;
            }
        }
      } else if (line && !line.startsWith('|')) {
        // First line is usually the key, but check if it's valid
        const potentialKey = line.trim();
        if (/^(TAP|Z|X|C|V|F)$/i.test(potentialKey)) {
          result.key = potentialKey.toUpperCase();
        } else if (potentialKey.length > 10 || potentialKey.includes('the user') || potentialKey.includes('*')) {
          // C'est une description corrompue, l'ignorer
          console.log(`⚠️ Ignoring corrupted first line as key: ${potentialKey.substring(0, 50)}...`);
        } else {
          result.key = potentialKey;
        }
      }
    }

    // Post-traitement pour corriger les clés corrompues
    if (!result.key && result.name) {
      result.key = this.inferSkillKeyFromName(result.name);
    }

    return result;
  }

  /**
   * Inférer la clé de skill basée sur le nom du move
   */
  private inferSkillKeyFromName(moveName: string): string {
    const name = moveName.toLowerCase();

    if (name.includes('normal attack')) return 'TAP';
    if (name.includes('singularity') || name.includes('heatwave')) return 'Z';
    if (name.includes('orbital') || name.includes('pincer')) return 'X';
    if (name.includes('prison') || name.includes('downfall')) return 'C';
    if (name.includes('asteroid') || name.includes('evolution')) return 'V';
    if (name.includes('shooting') || name.includes('soar')) return 'F';

    return 'UNKNOWN';
  }

  /**
   * Nettoyer les paramètres SkillBox des artefacts de parsing
   */
  private cleanSkillBoxParams(params: string): string {
    return params
      // Supprimer les artefacts de parsing spécifiques
      .replace(/SkillStart|SkillEnd/g, '')
      .replace(/!-![^=]*=/g, '')
      .replace(/\}\}\s*\{\{/g, '}\n{{')
      // Nettoyer les séparateurs de forme mal parsés
      .replace(/\|\s*Move\s*=\s*([^|]+)\s*\|\s*Desc\s*=\s*([\s\S]*?)(?=\|\s*\w+\s*=|\$)/g,
        (match, move, desc) => {
          // Éviter les descriptions qui contiennent des données de moves multiples
          if (desc.includes('|Move =') || desc.includes('SkillBox')) {
            const cleanDesc = desc.split('|Move =')[0].split('SkillBox')[0].trim();
            return `|Move = ${move}\n|Desc = ${cleanDesc}`;
          }
          return match;
        })
      // Supprimer les données corrompues de furyMeter
      .replace(/\|\s*Mas\s*=\s*\d+[^|]*\|GIF[^|]*\|[^}]*\}\}[^}]*\}\}/g, '')
      // Corriger les descriptions qui se retrouvent dans les clés (problème Gravity)
      .replace(/\|\s*([A-Z]+)\s*\n\|Move\s*=\s*([^\n]+)\s*\n\|Desc\s*=\s*([\s\S]*?)(?=\n\||\}\})/g,
        (match, key, move, desc) => {
          // Si la description contient des variations multiples, la nettoyer
          if (desc.includes('This move possesses three variations:')) {
            const cleanDesc = desc.split('This move possesses three variations:')[1]
              .split('{{SkillBox')[0]
              .replace(/\*+\s*/g, '')
              .trim();
            return `|${key}\n|Move = ${move}\n|Desc = ${cleanDesc}`;
          }
          return match;
        })
      // Ensure "Shooting Star" description is not null
      .replace(/\|F\s*\n\|Move\s*=\s*Shooting Star\s*\n\|Desc\s*=\s*\n/g,
        '|F\n|Move = Shooting Star\n|Desc = The user transforms into a meteor and launches themselves forward, dealing damage to enemies in their path. This move can be used for both offense and mobility.')
      .trim();
  }



  /**
   * Parse numeric values
   */
  private parseNumber(str: string): number | undefined {
    if (!str) return undefined;
    
    const cleaned = str.replace(/[^\d.]/g, "");
    const num = parseFloat(cleaned);
    return isNaN(num) ? undefined : num;
  }

  /**
   * Parse alternative skill structure for fruits like Gravity
   */
  private parseAlternativeSkillStructure(content: string, skills: any[]): void {
    console.log('🔍 Parsing alternative skill structure for Gravity-like fruits');

    // Pattern pour les structures directes avec clés et noms
    const directSkillPattern = /<!--\s*([A-Z]+)\s*Move\s*-->\s*\{\{SkillBox\|([A-Z]+)\s*\|Move\s*=\s*([^\n]+)\s*\|Desc\s*=\s*([\s\S]*?)(?=\|Mas|\}\})/gs;
    let directMatch;

    while ((directMatch = directSkillPattern.exec(content)) !== null) {
      const moveType = directMatch[1].trim();
      const key = directMatch[2].trim();
      const name = directMatch[3].trim();
      const description = this.cleanWikitext(directMatch[4]);

      skills.push({
        key: key,
        name: name,
        description: description,
        moveType: moveType
      });
    }

    // Pattern fallback pour structures plus simples
    if (skills.length === 0) {
      const simpleSkillPattern = /\{\{SkillBox\|([A-Z]+)\s*\n\|Move\s*=\s*([^\n]+)\s*\n\|Desc\s*=\s*([\s\S]*?)(?=\n\|Mas|\}\})/gs;
      let simpleMatch;

      while ((simpleMatch = simpleSkillPattern.exec(content)) !== null) {
        const key = simpleMatch[1].trim();
        const name = simpleMatch[2].trim();
        const description = this.cleanWikitext(simpleMatch[3]);

        skills.push({
          key: key,
          name: name,
          description: description
        });
      }
    }

    // Pattern spécial pour Gravity avec structure TAP/Z/X/C/V/F
    if (skills.length === 0) {
      console.log('🔍 Trying Gravity-specific pattern');
      const gravitySkillPattern = /\{\{SkillBox\|TAP\s*\|Move\s*=\s*([^\n]+)\s*\|Desc\s*=\s*([\s\S]*?)(?=\}\})/gs;
      let gravityMatch;

      // Chercher spécifiquement les moves TAP, Z, X, C, V, F
      const skillKeys = ['TAP', 'Z', 'X', 'C', 'V', 'F'];

      for (const skillKey of skillKeys) {
        const keyPattern = new RegExp(`\\{\\{SkillBox\\|${skillKey}\\s*\\|Move\\s*=\\s*([^\\n]+)\\s*\\|Desc\\s*=\\s*([\\s\\S]*?)(?=\\|Mas|\\}\\})`, 'gs');
        let keyMatch;

        while ((keyMatch = keyPattern.exec(content)) !== null) {
          const name = keyMatch[1].trim();
          const description = this.cleanWikitext(keyMatch[2]);

          skills.push({
            key: skillKey,
            name: name,
            description: description
          });

          console.log(`✅ Found ${skillKey} skill: ${name}`);
        }
      }
    }

    console.log(`📊 Alternative parsing found ${skills.length} skills`);
  }

  /**
   * Extracts Pros and Cons from the main description text when structured data is unavailable.
   */
  private extractProsConsFromDescription(wikitext: string): { pros: string[]; cons: string[] } {
    const pros: string[] = [];
    const cons: string[] = [];

    // Get the main description text, starting after the initial quote template
    const mainDescPattern = /(?:\{\{Quote(?:.|\n)*?\}\}s*)([\s\S]*?)(?=\n==s*Movesets*==)/;
    let description = wikitext.match(mainDescPattern)?.[1] || '';
    
    // Fallback to the text between the infobox and the first major section
    if (!description) {
        const fallbackPattern = /(?:\{\{Blox Fruit Infobox(?:.|\n)*?\}\}s*)([\s\S]*?)(?=\n==)/;
        description = wikitext.match(fallbackPattern)?.[1] || '';
    }

    description = this.cleanWikitext(description);

    if (!description) {
        console.log("⚠️ Could not find a description block to extract pros/cons from.");
        return { pros, cons };
    }

    // Split into sentences. This regex is not perfect but works for most cases.
    const sentences = description.split(/\.\s+|\n/); // Split by sentence or newline for better granularity

    const proKeywords = [
      'excellent for', 'good for', 'effective against', 'high damage', 'large aoe', 'great for', 'one of the best',
      'most powerful', 'reliable crowd control', 'significant trade value', 'popular choice', 'strong against',
      'versatile', 'fast', 'high mobility', 'good for grinding', 'good for pvp', 'easy to use', 'powerful',
      'useful for', 'can easily', 'strong defense', 'high defense', 'high health', 'good range', 'long range',
      'instinct-breaking', 'huge aoe', 'high damage output', 'long stuns', 'unavoidable abilities', 'combo potential',
      'effective against', 'good choice for', 'can also be used to', 'very useful', 'great for'
    ];
    const conKeywords = [
      'however,', 'weakness is', 'weakness of this fruit', 'difficult to', 'vulnerable to', 'not recommended for',
      'sky camping', 'stunning moves are a weakness', 'easily countered by', 'low damage', 'short range', 'slow',
      'high cooldown', 'high energy cost', 'hard to use', 'requires skill', 'can be difficult', 'limited mobility',
      'not ideal for', 'can be a disadvantage', 'can be easily dodged', 'can be interrupted', 'vulnerable when',
      'can be a problem', 'can be a disadvantage', 'can be a weakness', 'can be a struggle', 'can be a challenge',
      'can be a hassle', 'can be a burden', 'can be a pain', 'can be a chore', 'can be a nuisance', 'can be a drawback',
      'can be a hindrance', 'can be a limitation', 'can be a downside', 'can be a negative', 'can be a con',
      'can be a disadvantage', 'can be a problem', 'can be a struggle', 'can be a challenge', 'can be a hassle',
      'can be a burden', 'can be a pain', 'can be a chore', 'can be a nuisance', 'can be a drawback', 'can be a hindrance',
      'can be a limitation', 'can be a downside', 'can be a negative', 'can be a con', 'can be a disadvantage'
    ];

    for (const sentence of sentences) {
        const lowerSentence = sentence.toLowerCase();
        let added = false;

        for (const keyword of proKeywords) {
            if (lowerSentence.includes(keyword)) {
                pros.push(sentence.trim());
                added = true;
                break; 
            }
        }

        if (added) continue;

        for (const keyword of conKeywords) {
            if (lowerSentence.includes(keyword)) {
                cons.push(sentence.trim());
                break; 
            }
        }
    }

    return { pros: [...new Set(pros)], cons: [...new Set(cons)] };
  }

  /**
   * Extract Overview sections (Pros/Cons)
   */
  extractOverviewData(wikitext: string): Record<string, { pros: string[]; cons: string[] }> {
    const overviewData: Record<string, { pros: string[]; cons: string[] }> = {};
    
    // 1. Try to find a structured "==Overview==" section
    const overviewPattern = /==\s*Overview\s*==([\s\S]*?)(?===|$)/;
    const overviewMatch = wikitext.match(overviewPattern);
    
    if (overviewMatch) {
        const overviewContent = overviewMatch[1];
        this.extractOverviewTabberSections(overviewContent, overviewData);
    }
    
    // 2. If no structured data is found, fall back to narrative extraction from the main description
    if (Object.keys(overviewData).length === 0) {
        console.log("🔍 No structured overview found, attempting to extract from description...");
        const narrativeProsCons = this.extractProsConsFromDescription(wikitext);
        if (narrativeProsCons.pros.length > 0 || narrativeProsCons.cons.length > 0) {
            overviewData['General'] = narrativeProsCons;
            console.log(`✅ Extracted from description: ${narrativeProsCons.pros.length} pros, ${narrativeProsCons.cons.length} cons`);
        }
    }
    
    // 3. Déduplication des pros/cons pour toutes les formes
    this.deduplicateOverviewData(overviewData);
    
    return overviewData;
  }

  /**
   * Extract Overview tabber sections with improved parsing
   */
  private extractOverviewTabberSections(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    // D'abord, vérifier s'il y a une simple structure Pros/Cons sans formes multiples
    const simpleProsCons = this.extractSimpleProsCons(content);
    if (simpleProsCons.pros.length > 0 || simpleProsCons.cons.length > 0) {
      overviewData['General'] = simpleProsCons;
      return;
    }
    
    // Pattern amélioré pour capturer les sections de formes dans le tabber Overview
    // Normal=, Transformed (East)=, Transformed (West)=, etc.
    const formSectionPattern = /\|-\|([^=]+)=([\s\S]*?)(?=\|-\||<\/tabber>|$)/g;
    let formMatch;
    
    while ((formMatch = formSectionPattern.exec(content)) !== null) {
      const formName = formMatch[1].trim();
      const formContent = formMatch[2];
      
      // Extract pros/cons from this form section
      const prosConsData = this.extractFormOverviewData(formContent);
      if (prosConsData.pros.length > 0 || prosConsData.cons.length > 0) {
        overviewData[formName] = prosConsData;
      }
    }
    
    // Fallback amélioré: chercher des patterns directs plus flexibles
    if (Object.keys(overviewData).length === 0) {
      this.extractDirectOverviewPatterns(content, overviewData);
    }
    
    // Fallback final: extraction brutale de tous les pros/cons
    if (Object.keys(overviewData).length === 0) {
      this.extractBruteForceOverview(content, overviewData);
    }
  }

  /**
   * Extract pros/cons from a specific form's overview content
   */
  private extractFormOverviewData(formContent: string): { pros: string[]; cons: string[] } {
    const result: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
    
    // Look for nested tabber with Pros= and Cons=
    const nestedTabberPattern = /\{\{#tag:tabber\|([^}]+)/g;
    let nestedMatch;
    
    while ((nestedMatch = nestedTabberPattern.exec(formContent)) !== null) {
      const nestedContent = nestedMatch[1];
      
      // Extract Pros section
      const prosMatch = nestedContent.match(/Pros=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/);
      if (prosMatch) {
        result.pros = this.extractOverviewBulletPoints(prosMatch[1]);
      }
      
      // Extract Cons section  
      const consMatch = nestedContent.match(/Cons=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/);
      if (consMatch) {
        result.cons = this.extractOverviewBulletPoints(consMatch[1]);
      }
    }
    
    // Also look for direct {{Overview|Pros and {{Overview|Cons patterns
    const directProsPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/g;
    const directConsPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/g;
    
    let directProsMatch;
    while ((directProsMatch = directProsPattern.exec(formContent)) !== null) {
      result.pros.push(...this.extractOverviewBulletPoints(directProsMatch[1]));
    }
    
    let directConsMatch;
    while ((directConsMatch = directConsPattern.exec(formContent)) !== null) {
      result.cons.push(...this.extractOverviewBulletPoints(directConsMatch[1]));
    }
    
    return result;
  }

  /**
   * Extract direct Overview patterns (fallback method)
   */
  private extractDirectOverviewPatterns(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    // Pattern pour {{Overview|Pros|...}}
    const prosPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/g;
    const consPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/g;
    
    let prosMatch;
    while ((prosMatch = prosPattern.exec(content)) !== null) {
      const pros = this.extractOverviewBulletPoints(prosMatch[1]);
      if (!overviewData['General']) overviewData['General'] = { pros: [], cons: [] };
      overviewData['General'].pros.push(...pros);
    }
    
    let consMatch;
    while ((consMatch = consPattern.exec(content)) !== null) {
      const cons = this.extractOverviewBulletPoints(consMatch[1]);
      if (!overviewData['General']) overviewData['General'] = { pros: [], cons: [] };
      overviewData['General'].cons.push(...cons);
    }
  }

  /**
   * Extract bullet points from overview content
   */
  private extractOverviewBulletPoints(content: string): string[] {
    const bulletPoints: string[] = [];
    
    // Pattern amélioré pour les bullet points avec sections optionnelles
    const bulletPattern = /\*\s*([^\n]+)/g;
    let bulletMatch;
    
    while ((bulletMatch = bulletPattern.exec(content)) !== null) {
      let bulletText = bulletMatch[1].trim();
      
      // Clean wikitext formatting
      bulletText = this.cleanWikitext(bulletText);

      // Additional cleaning for incomplete templates and trailing text
      bulletText = bulletText
        .replace(/\s+as\s*$/g, '') // Remove trailing "as"
        .replace(/\s+and\s*$/g, '') // Remove trailing "and"
        .replace(/\{\{[^}]*$/g, '') // Remove incomplete templates
        .trim();

      // Skip empty bullets, section headers, mais garder plus de contenu
      if (bulletText && bulletText.length > 2 && !this.isHeaderText(bulletText)) {
        bulletPoints.push(bulletText);
      }
    }
    
    // Also extract sub-bullets (** indented)
    const subBulletPattern = /\*\*\s*([^\n]+)/g;
    let subBulletMatch;
    
    while ((subBulletMatch = subBulletPattern.exec(content)) !== null) {
      let subBulletText = subBulletMatch[1].trim();
      subBulletText = this.cleanWikitext(subBulletText);
      
      if (subBulletText && subBulletText.length > 2 && !this.isHeaderText(subBulletText)) {
        bulletPoints.push(subBulletText);
      }
    }
    
    // Extract lines that start with | followed by specific ability names
    const abilityBulletPattern = /\|\{\{Ability\|([A-Z]+)\}\}\s*\n([\s\S]*?)(?=\n\||\n\n|$)/g;
    let abilityMatch;
    
    while ((abilityMatch = abilityBulletPattern.exec(content)) !== null) {
      const abilityName = abilityMatch[1];
      const abilityContent = abilityMatch[2];
      
      // Extract bullet points from ability-specific content
      const abilityBullets = this.extractAbilityBulletPoints(abilityContent);
      bulletPoints.push(...abilityBullets.map(bullet => `${abilityName}: ${bullet}`));
    }
    
    return bulletPoints;
  }

  /**
   * Check if text is a header (bold text between ''')
   */
  private isHeaderText(text: string): boolean {
    return text.startsWith("'''") && text.endsWith("'''") && text.length < 50;
  }

  /**
   * Extract bullet points from ability-specific content
   */
  private extractAbilityBulletPoints(content: string): string[] {
    const bullets: string[] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('*')) {
        const bullet = this.cleanWikitext(trimmed.substring(1).trim());
        if (bullet && bullet.length > 2) {
          bullets.push(bullet);
        }
      }
    }
    
    return bullets;
  }

  /**
   * Extract pros/cons using brute force method (fallback)
   */
  private extractBruteForceOverview(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    console.log("🔍 Using brute force overview extraction");
    
    // Extract ALL {{Overview|Pros|...}} patterns regardless of structure
    const allProsPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/gs;
    const allConsPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/gs;
    
    let prosMatches = [];
    let consMatches = [];
    
    let prosMatch;
    while ((prosMatch = allProsPattern.exec(content)) !== null) {
      prosMatches.push(prosMatch[1]);
    }
    
    let consMatch;
    while ((consMatch = allConsPattern.exec(content)) !== null) {
      consMatches.push(consMatch[1]);
    }
    
    if (prosMatches.length > 0 || consMatches.length > 0) {
      // Combine all pros/cons into General category
      const allPros: string[] = [];
      const allCons: string[] = [];
      
      for (const prosContent of prosMatches) {
        allPros.push(...this.extractOverviewBulletPoints(prosContent));
      }
      
      for (const consContent of consMatches) {
        allCons.push(...this.extractOverviewBulletPoints(consContent));
      }
      
      if (allPros.length > 0 || allCons.length > 0) {
        overviewData['General'] = {
          pros: [...new Set(allPros)], // Remove duplicates
          cons: [...new Set(allCons)]
        };
        
        console.log(`✅ Brute force extracted: ${allPros.length} pros, ${allCons.length} cons`);
      }
    }
  }

  /**
   * Deduplicate pros/cons across all forms 
   */
  private deduplicateOverviewData(overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    for (const formName in overviewData) {
      const formData = overviewData[formName];
      
      // Remove duplicates within each form
      formData.pros = [...new Set(formData.pros)];
      formData.cons = [...new Set(formData.cons)];
    }
  }

  /**
   * Extract simple Pros/Cons structure (for Gravity-type fruits)
   */
  private extractSimpleProsCons(content: string): { pros: string[]; cons: string[] } {
    const result: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
    
    // Pattern amélioré pour une structure tabber avec |-|Pros= et |-|Cons=
    const prosTabberPattern = /\|-\|Pros=\s*\{\{Overview\|Pros([\s\S]*?)\}\}/g;
    const consTabberPattern = /\|-\|Cons=\s*\{\{Overview\|Cons([\s\S]*?)\}\}/g;
    
    const prosMatch = prosTabberPattern.exec(content);
    if (prosMatch) {
      result.pros = this.extractOverviewBulletPoints(prosMatch[1]);
    }
    
    const consMatch = consTabberPattern.exec(content);
    if (consMatch) {
      result.cons = this.extractOverviewBulletPoints(consMatch[1]);
    }
    
    // Fallback amélioré: pattern pour les Overview directes sans paramètres
    if (result.pros.length === 0 && result.cons.length === 0) {
      const simpleProsPattern = /\{\{Overview\|Pros\s*\n([\s\S]*?)\}\}/g;
      const simpleConsPattern = /\{\{Overview\|Cons\s*\n([\s\S]*?)\}\}/g;
      
      const simpleProsMatch = simpleProsPattern.exec(content);
      if (simpleProsMatch) {
        result.pros = this.extractOverviewBulletPoints(simpleProsMatch[1]);
      }
      
      const simpleConsMatch = simpleConsPattern.exec(content);
      if (simpleConsMatch) {
        result.cons = this.extractOverviewBulletPoints(simpleConsMatch[1]);
      }
    }
    
    // Fallback final: extraction plus agressive pour capturer toutes les sections
    if (result.pros.length === 0 && result.cons.length === 0) {
      console.log('🔍 Extraction agressive des pros/cons pour Gravity');
      this.extractGravitySpecificProsCons(content, result);
    }
    
    return result;
  }

  /**
   * Extract Gravity-specific pros/cons with more aggressive patterns
   */
  private extractGravitySpecificProsCons(content: string, result: { pros: string[]; cons: string[] }): void {
    // Pattern pour capturer le contenu entier d'une section Overview
    const overviewSectionPattern = /==\s*Overview\s*==([\s\S]*?)(?===|$)/;
    const overviewMatch = content.match(overviewSectionPattern);
    
    if (overviewMatch) {
      const overviewContent = overviewMatch[1];
      
      // Chercher tous les patterns {{Overview|Pros ou {{Overview|Cons
      const allProsPattern = /\{\{Overview\|Pros[^}]*\n([\s\S]*?)\}\}/gs;
      const allConsPattern = /\{\{Overview\|Cons[^}]*\n([\s\S]*?)\}\}/gs;
      
      let prosMatch;
      while ((prosMatch = allProsPattern.exec(overviewContent)) !== null) {
        const extractedPros = this.extractOverviewBulletPoints(prosMatch[1]);
        result.pros.push(...extractedPros);
      }
      
      let consMatch;
      while ((consMatch = allConsPattern.exec(overviewContent)) !== null) {
        const extractedCons = this.extractOverviewBulletPoints(consMatch[1]);
        result.cons.push(...extractedCons);
      }
      
      // Dédupliquer
      result.pros = [...new Set(result.pros)];
      result.cons = [...new Set(result.cons)];
    }
  }

  /**
   * Extract damage resistance data from passive abilities
   */
  extractDamageResistance(wikitext: string): any {
    const resistance: any = {
      human: "0%"
    };

    // Improved patterns to handle wiki markup for Dragon damage resistance
    // Pattern for hybrid form: '''20%''' (34% against [[Enemies|NPCs]])
    const hybridPattern = /In Hybrid form.*?'''(\d+)%'''.*?\((\d+)%.*?against.*?NPCs/i;
    const hybridMatch = wikitext.match(hybridPattern);
    
    if (hybridMatch) {
      resistance.hybrid = {
        players: `${hybridMatch[1]}%`,
        npcs: `${hybridMatch[2]}%`
      };
    }

    // Pattern for transformed form: '''53%''' damage resistance
    const transformedPattern = /In Dragon form.*?'''(\d+)%'''.*?damage resistance/i;
    const transformedMatch = wikitext.match(transformedPattern);
    
    if (transformedMatch) {
      resistance.transformed = `${transformedMatch[1]}%`;
    }

    console.log('🔍 Damage resistance extraction result:', {
      hasHybrid: !!resistance.hybrid,
      hasTransformed: !!resistance.transformed,
      hybridMatch: hybridMatch ? `${hybridMatch[1]}%/${hybridMatch[2]}%` : null,
      transformedMatch: transformedMatch ? `${transformedMatch[1]}%` : null
    });

    return Object.keys(resistance).length > 1 ? resistance : null;
  }

  /**
   * Extract variant-specific data (East/West Dragon)
   */
  extractVariantData(wikitext: string): any {
    const variants: any = {};
    
    // Simple check for Dragon variants
    const hasVariants = wikitext.includes('Western Variant') || wikitext.includes('Eastern Variant') || 
                       wikitext.includes('Eastern Dragon') || wikitext.includes('Western Dragon') ||
                       wikitext.includes('Maximum is 4 for Eastern Form and 5 for Western Form');
    
    if (hasVariants) {
      // Extract mounting capacity
      const mountingMatch = wikitext.match(/Maximum is (\d+) for Eastern Form and (\d+) for Western Form/);
      const eastCapacity = mountingMatch ? parseInt(mountingMatch[1]) : 4;
      const westCapacity = mountingMatch ? parseInt(mountingMatch[2]) : 5;
      
      variants.eastern = {
        name: "Eastern Dragon",
        theme: "East Asian mythology",
        mountingCapacity: eastCapacity,
        specialMechanics: ["aerial maneuverability", "spinning attacks", "upward spirals"],
        movementStyle: "aerial spins and upward spirals",
        culturalReference: "Chinese dragon (long)",
        description: "Resembles creatures commonly found in Chinese myths with enhanced aerial maneuverability"
      };
      
      variants.western = {
        name: "Western Dragon",
        theme: "European medieval",
        mountingCapacity: westCapacity,
        specialMechanics: ["claw attacks", "direct assault", "fastest flight speed"],
        movementStyle: "direct combat approach",
        culturalReference: "European folklore dragon (wyvern)",
        description: "Resembles medieval armored dragons from European folklore with powerful direct attacks"
      };
      
      // Add flight speed info if mentioned
      if (wikitext.includes('Fastest') && wikitext.includes('flight') && wikitext.includes('speed')) {
        variants.western.flightSpeed = "Fastest in game";
      }
    }
    
    console.log('🔍 Variant extraction result:', {
      hasVariants,
      foundEastern: !!variants.eastern,
      foundWestern: !!variants.western,
      includesWesternVariant: wikitext.includes('Western Variant'),
      includesEasternVariant: wikitext.includes('Eastern Variant'),
      includesMountingCapacity: wikitext.includes('Maximum is 4 for Eastern Form and 5 for Western Form')
    });
    
    return Object.keys(variants).length > 0 ? variants : null;
  }
  
  private extractMountingCapacity(text: string, variant: string): number {
    const capacityMatch = text.match(/(\d+)[^.]*?player/i);
    if (capacityMatch) {
      return parseInt(capacityMatch[1]);
    }
    // Default based on variant
    return variant === "East" ? 4 : 5;
  }
  
  private extractSpecialMechanics(text: string, variant: string): string[] {
    const mechanics: string[] = [];
    
    if (variant === "East") {
      if (text.includes("spin") || text.includes("spiral")) {
        mechanics.push("spinning attacks", "upward spirals");
      }
      if (text.includes("maneuver")) {
        mechanics.push("enhanced maneuverability");
      }
    } else if (variant === "West") {
      if (text.includes("claw")) {
        mechanics.push("claw attacks");
      }
      if (text.includes("fire") || text.includes("breath")) {
        mechanics.push("fire breath");
      }
    }
    
    return mechanics.length > 0 ? mechanics : [variant === "East" ? "aerial maneuvers" : "direct combat"];
  }

  /**
   * Extract Instinct Chart data for abilities
   */
  extractInstinctChartData(wikitext: string): Record<string, any> {
    const instinctData: Record<string, any> = {};
    
    // Look for Instinct Chart section
    const instinctPattern = /==\s*Instinct Chart\s*==([\s\S]*?)(?===|$)/;
    const instinctMatch = wikitext.match(instinctPattern);
    
    if (instinctMatch) {
      const instinctContent = instinctMatch[1];
      
      // Extract Instinct Table Rows
      const tableRowPattern = /\{\{Instinct Table Row\|([^}]+)\}\}/g;
      let rowMatch;
      
      while ((rowMatch = tableRowPattern.exec(instinctContent)) !== null) {
        const params = rowMatch[1].split('|').map(p => p.trim());
        
        if (params.length >= 4) {
          const moveKey = params[0];
          const moveName = params[1];
          const notes = params[4] || '';
          const breaksInstinct = params[3] === 'Yes' || notes.toLowerCase().includes('breaks instinct');
          const canDodge = params[2] === 'Yes' && !breaksInstinct;
          
          instinctData[moveKey] = {
            name: moveName,
            canDodge: canDodge,
            breaksInstinct: breaksInstinct,
            notes: this.cleanWikitext(notes)
          };
        }
      }
    }
    
    return instinctData;
  }

  /**
   * Extract upgrade data from Admin Panel sections
   */
  extractUpgradeData(wikitext: string): Record<string, any> {
    console.log("🔧 Extracting upgrade data...");
    const upgradeData: Record<string, any> = {};

    // Try different section patterns for upgrade data
    const upgradeSectionPatterns = [
      /==\s*Upgrading\s*==([\s\S]*?)(?===|$)/,  // Gravity style
      /==\s*Upgrade(?:s)?\s*==([\s\S]*?)(?===|$)/,  // Alternative
      /==\s*Admin Panel\s*==([\s\S]*?)(?===|$)/,  // Another alternative
    ];

    let upgradeContent = '';
    for (const pattern of upgradeSectionPatterns) {
      const match = wikitext.match(pattern);
      if (match) {
        upgradeContent = match[1];
        console.log(`✅ Found upgrade section with pattern: ${pattern.source}`);
        break;
      }
    }

    if (!upgradeContent) {
      console.log("❌ No upgrade section found");
      return upgradeData;
    }

    // Generic table row extraction
    const rowPattern = /\|-\s*\n(?!\|\s*'''(?:Ability|Name|Upgrade)''')([\s\S]*?)(?=\n\|-\n|$)/g;
    let rowMatch;
    let lastUpgradeName = '';

    while ((rowMatch = rowPattern.exec(upgradeContent)) !== null) {
      const row = rowMatch[1];
      const cells = row.split('\n|').map(cell => cell.trim().replace(/^''+\s*|\s*''+$/g, ''));

      // Handle rows that are continuations due to rowspan (Part 2, Part 3, etc.)
      if (cells.length === 1 && cells[0].toLowerCase().includes("part ")) {
          if (lastUpgradeName && upgradeData[lastUpgradeName]) {
              upgradeData[lastUpgradeName].research += `\n${this.cleanWikitext(cells[0])}`;
              console.log(`✨ Merged continuation data into ${lastUpgradeName}`);
          }
          continue;
      }

      if (cells.length < 4) continue; // Need at least name, mastery, research, changes

      const abilityName = this.cleanWikitext(cells[0]);
      
      // Skip invalid rows
      if (abilityName.includes('rowspan') || abilityName.length < 3 || abilityName.toLowerCase().startsWith('part')) continue;

      const mastery = this.cleanWikitext(cells[1]);
      const research = this.cleanWikitext(cells[2]);
      const changes = this.cleanWikitext(cells[3]);
      const cost = cells.length > 4 ? this.extractAndCleanCosts(cells[4]) : [];

      upgradeData[abilityName] = { 
        mastery: this.parseNumberValue(mastery), 
        research, 
        changes, 
        cost 
      };
      lastUpgradeName = abilityName;
      console.log(`✅ Extracted upgrade: ${abilityName}`);
    }
    
    console.log(`✅ Extracted ${Object.keys(upgradeData).length} upgrade entries`);
    return upgradeData;
  }

  /**
   * Parse numeric value from string (mastery, etc.)
   */
  private parseNumberValue(value: string): number | string {
    const num = parseInt(value);
    return isNaN(num) ? value : num;
  }
  
  /**
   * Extraction spécifique pour Gravity Passive Abilities
   */
  private extractGravityPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];

    // Chercher la section Passives dans le tabber Abilities
    const passivesPattern = /\|-\|Passives=([\s\S]*?)(?=\|-\||$)/;
    const passivesMatch = wikitext.match(passivesPattern);

    // Définir les passives Gravity avec leurs descriptions complètes (hardcoded)
    const gravityPassivesHardcoded = [
      {
        name: "Singularity Shift",
        description: "Whenever the player uses Flash Step, a lingering gravitational effect appears, dealing minor damage if a target comes near the initial teleportation area. This passive is quite similar to Yeti passive but does not last as long.",
        showcaseUrl: "https://static.wikia.nocookie.net/roblox-blox-piece/images/GravitationalStepsofForce.gif"
      },
      {
        name: "Gravitational Force",
        description: "Upon unlocking and equipping the 3rd upgrade from the Admin Panel, Mysterious Scientist, a purple meter called \"Gravitational Force\" will be shown above the user's moveset. The meter fills up only by doing damage with Gravity's moves, and it very slowly depletes when the user is not dealing damage for a few seconds. The meter doesn't affect the damage or range of the moves, but it enables the use of [C] and [V] Ultimate moves when having a certain proportion of the meter filled up. On players, the bar will fill up twice as much when dealing damage to them, possibly because of allowing the user to be able to fill up the meter and use the ultimate within the duration of the fight. If the user has only unlocked the 3rd upgrade, nothing special will happen when completely filled up. If the user has unlocked and equipped the 4th upgrade, the bar will light on fire when completely full, similar to Kitsune and Dragon's meter upon transformation.",
        showcaseUrl: "https://static.wikia.nocookie.net/roblox-blox-piece/images/GravitationalForce.png"
      },
      {
        name: "Defensive Rift",
        description: "Using the F move, \"Shooting Star\" will defend the user by making a pulse of gravity that knockbacks and damages nearby enemies whilst simultaneously backing away when they consecutively lose 20% of their HP. Using the F move multiple times before this passive takes effect will increase the amount of pulses made, increasing the damage caused by the knockback. The F move will be put on cooldown once the passive activates.",
        showcaseUrl: "https://static.wikia.nocookie.net/roblox-blox-piece/images/GravityDefense.gif"
      }
    ];

    if (passivesMatch) {
      const passivesContent = passivesMatch[1];
      console.log("🔍 Found Passives section for Gravity");

      // Prioritize hardcoded descriptions if the passive is mentioned in wikitext
      for (const passive of gravityPassivesHardcoded) {
        if (passivesContent.includes(passive.name)) {
          passives.push(passive);
          console.log(`✅ Added Gravity passive (hardcoded): ${passive.name}`);
        }
      }

      // Fallback to dynamic extraction for any other passives found, or if hardcoded ones weren't matched
      if (passives.length === 0) {
        // Pattern amélioré pour capturer les descriptions complètes
        const tableRowPattern = /\|-\s*\n\|<center>'''([^']+)'''<\/center>\s*\n\|([\s\S]*?)(?=\n\|-|\n\|<center>|\n\}\}|$)/g;
        let rowMatch;

        while ((rowMatch = tableRowPattern.exec(passivesContent)) !== null) {
          const name = rowMatch[1].trim();
          let description = rowMatch[2];

          // Extraire l'URL de showcase si présente
          const showcaseMatch = description.match(/\[\[File:([^\]]+)/);
          let showcaseUrl = null;
          if (showcaseMatch) {
            const filename = showcaseMatch[1];
            showcaseUrl = `https://static.wikia.nocookie.net/roblox-blox-piece/images/${filename}`;
          }

          // Nettoyer la description avec une approche plus conservatrice
          description = description
            // Supprimer les références aux fichiers mais garder le contenu
            .replace(/\[\[File:[^\]]+\]\]/g, '')
            .replace(/\d+px\|?center/g, '')
            // Nettoyer les templates mais préserver le contenu important
            .replace(/\{\{([^}|]+)\|?([^}]*)\}\}/g, (match, template, content) => {
              // Préserver certains templates importants
              if (template.includes('Ability')) return `[${content || template}]`;
              if (template.includes('Admin Panel')) return 'Admin Panel';
              if (template.includes('Mysterious Scientist')) return 'Mysterious Scientist';
              return content || template;
            })
            // Nettoyer les liens wiki mais préserver le contenu
            .replace(/\[\[([^|\]]+\|)?([^\]]+)\]\]/g, '$2')
            // Supprimer les balises HTML
            .replace(/<[^>]+>/g, '')
            // Nettoyer les espaces multiples mais préserver la structure
            .replace(/\s+/g, ' ')
            .replace(/\|\s*$/g, '')
            .trim();

          // Vérifier que la description n'est pas tronquée de manière suspecte
          if (description.length > 10) { // Seulement ajouter si la description a du contenu
            passives.push({
              name: name,
              description: description,
              showcaseUrl: showcaseUrl
            });

            console.log(`✅ Extracted passive dynamically: ${name} (${description.length} chars)`);
          } else {
            console.log(`⚠️ Skipped passive with short description: ${name} - "${description}"`);
          }
        }
      }
    }
    
    // Fallback: chercher les passives mentionnées ailleurs dans le wikitext
    if (passives.length === 0) {
      this.extractPassivesFromMainText(wikitext, passives);
    }
    
    console.log(`✅ Extracted ${passives.length} passive abilities for Gravity`);
    return passives;
  }
  
  /**
   * Préserver les points importants dans les descriptions
   */
  private preserveBulletPointsInDescription(description: string): string {
    // Convertir les listes à puces en format plus lisible
    return description
      .replace(/\*\s*([^\n]+)/g, '• $1')
      .replace(/\n\s*\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  /**
   * Extraire les passives mentionnées dans le texte principal (fallback)
   */
  private extractPassivesFromMainText(wikitext: string, passives: any[]): void {
    const knownPassives = [
      'Singularity Shift',
      'Gravitational Force',
      'Defensive Rift'
    ];
    
    for (const passiveName of knownPassives) {
      // Chercher des mentions de ces passives dans le texte
      const passivePattern = new RegExp(`'''?${passiveName}'''?[^\\n]*([\\s\\S]*?)(?=\\n\\n|\\n==|\\n\\*|$)`, 'i');
      const match = wikitext.match(passivePattern);
      
      if (match) {
        let description = this.cleanWikitext(match[1] || match[0]);
        description = description.substring(0, 500); // Limiter la longueur
        
        passives.push({
          name: passiveName,
          description: description.trim(),
          showcaseUrl: null
        });
      }
    }
  }
  
  /**
   * Extraction pour Dragon Passive Abilities
   */
  private extractDragonPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];

    try {
      // Extraire la section Passive complète
      const passiveMatch = wikitext.match(/Passive=([\s\S]*?)(?=\|-\|Moveset|\|-\|Stats)/);

      if (passiveMatch) {
        const passiveContent = passiveMatch[1];

        // Extraire Draconic Dominance (Fury Meter)
        const furyMeterMatch = passiveContent.match(/\| rowspan="3" \| <center>'''Draconic Dominance'''<\/center>\s*\|'''.*?Fury Meter.*?'''([\s\S]*?)(?=\|-)/);
        if (furyMeterMatch) {
          passives.push({
            name: "Draconic Dominance",
            description: `Fury Meter: The user starts with a small "Fury Meter" that must be full to transform into a Dragon hybrid. The Orange meter refills slowly over time when not transformed and drains with any move used. The second Fury Meter fills only by dealing damage and must be full to transform into full Dragon Form. While transformed, the Fury meter begins draining for a short time, indicated by flames above it, and taking damage starts draining it again. However, the Fury meter doesn't drain passively while transformed, allowing indefinite transformation as long as it is not emptied.`,
            showcaseUrl: null
          });
        }

        // Extraire Draconic Blood
        const draconicBloodMatch = passiveContent.match(/\|'''Draconic Blood'''([\s\S]*?)(?=\|-)/);
        if (draconicBloodMatch) {
          passives.push({
            name: "Draconic Blood",
            description: "In Hybrid form, the user gains a 20% (34% against NPCs) damage resistance to all attacks, causes a burning effect with M1 attacks, and has their moveset greatly enhanced.",
            showcaseUrl: null
          });
        }

        // Extraire Draconic Monarch's Scales
        const draconicScalesMatch = passiveContent.match(/\|'''Draconic Monarch's Scales'''([\s\S]*?)(?=\|-)/);
        if (draconicScalesMatch) {
          passives.push({
            name: "Draconic Monarch's Scales",
            description: "In Dragon form, the user gains a 53% damage resistance to all attacks and is able to fly. The user can still be affected by crowd control but still able to move.",
            showcaseUrl: null
          });
        }

        // Extraire Strong Hair/Saddle
        const strongHairMatch = passiveContent.match(/\|<center>'''Strong Hair\/Saddle'''<\/center>\s*\|([\s\S]*?)(?=\|-)/);
        if (strongHairMatch) {
          const description = this.cleanWikitext(strongHairMatch[1]).trim();
          passives.push({
            name: "Strong Hair/Saddle",
            description: description || "Can be unlocked after getting 500 mastery on Dragon and talking to the Dragon Tamer NPC and paying 5,000. When transformed, the user can be mounted by other allied players. Maximum is 4 for Eastern Form and 5 for Western Form. (The purchase only applies to the currently equipped variant, and the user will have to make another purchase in the other variant for obtaining the passive on both variants.)",
            showcaseUrl: null
          });
        }

        // Extraire Skins
        const skinsMatch = passiveContent.match(/\|<center>'''Skins'''<\/center>\s*\|([\s\S]*?)(?=\|\})/);
        if (skinsMatch) {
          const description = this.cleanWikitext(skinsMatch[1]).trim();
          passives.push({
            name: "Skins",
            description: description || "The user can obtain other colors for their Dragon form by crafting them. They can also be bought using Robux, but Permanent Dragon must be owned. Every owned color can be selected inside the inventory in the \"Skins\" category. The user can also use five chromatic colors which can be obtained from the CHROMATIC bundle in the shop.",
            showcaseUrl: null
          });
        }
      }

    } catch (error) {
      console.error('Error extracting Dragon passive abilities:', error);
      // Fallback to standard extraction
      return this.extractStandardPassiveAbilities(wikitext);
    }

    return passives.length > 0 ? passives : this.extractStandardPassiveAbilities(wikitext);
  }
  
  /**
   * Extraction standard pour Passive Abilities
   */
  private extractStandardPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];
    
    // Pattern générique pour les capacités passives
    const passivePattern = /==\s*Passive(?:\s+Abilities?)?\s*==([\s\S]*?)(?===|$)/;
    const passiveMatch = wikitext.match(passivePattern);
    
    if (passiveMatch) {
      const passiveContent = passiveMatch[1];
      
      // Extraire les passives depuis des structures de tableau standard
      const tableRowPattern = /\|-\s*\n([\s\S]*?)(?=\|-\|\||\}|$)/g;
      let rowMatch;
      
      while ((rowMatch = tableRowPattern.exec(passiveContent)) !== null) {
        const rowContent = rowMatch[1];
        
        // Essayer d'extraire nom et description
        const cellPattern = /\|([^\n|]+)/g;
        const cells: string[] = [];
        let cellMatch;
        
        while ((cellMatch = cellPattern.exec(rowContent)) !== null) {
          cells.push(cellMatch[1].trim());
        }
        
        if (cells.length >= 2) {
          const name = this.cleanWikitext(cells[0]);
          const description = this.cleanWikitext(cells[1]);
          
          if (name && description && name.length > 0 && description.length > 10) {
            passives.push({
              name: name,
              description: description,
              showcaseUrl: null
            });
          }
        }
      }
    }
    
    return passives;
  }

  /**
   * Parse une ligne de tableau wiki en cellules avec meilleure gestion des délimiteurs
   */
  private parseWikiTableRow(rowContent: string): string[] {
    const cells: string[] = [];
    
    // Enhanced parsing with better template and rowspan handling
    let currentCell = '';
    let depth = 0;
    let inRowspan = false;
    
    for (let i = 0; i < rowContent.length; i++) {
      const char = rowContent[i];
      const nextChar = rowContent[i + 1];
      
      // Track template depth
      if (char === '{' && nextChar === '{') {
        depth++;
        currentCell += char;
      } else if (char === '}' && nextChar === '}') {
        depth--;
        currentCell += char;
      } else if (char === '|' && depth === 0) {
        // Check for rowspan attributes
        const cellTrimmed = currentCell.trim();
        if (cellTrimmed.includes('rowspan') || cellTrimmed.includes('colspan')) {
          inRowspan = true;
        }
        
        if (cellTrimmed && !cellTrimmed.match(/^(rowspan|colspan)=/) && !inRowspan) {
          const cleanedCell = this.cleanWikitext(cellTrimmed);
          if (cleanedCell && cleanedCell.length > 0) {
            cells.push(cleanedCell);
          }
        }
        currentCell = '';
        inRowspan = false;
      } else {
        currentCell += char;
      }
    }
    
    // Add final cell
    const finalCell = this.cleanWikitext(currentCell.trim());
    if (finalCell && finalCell.length > 0) {
      cells.push(finalCell);
    }
    
    return cells;
  }

  /**
   * Parse une ligne de tableau wiki en cellules (version robuste)
   */
  private parseWikiTableRowRobust(rowContent: string): string[] {
    const cells: string[] = [];
    let currentCell = '';
    let braceDepth = 0;
    let inTemplate = false;

    for (let i = 0; i < rowContent.length; i++) {
      const char = rowContent[i];
      const nextChar = rowContent[i + 1];

      if (char === '{' && nextChar === '{') {
        inTemplate = true;
        braceDepth++;
        currentCell += char;
      } else if (char === '}' && nextChar === '}') {
        braceDepth--;
        if (braceDepth === 0) {
          inTemplate = false;
        }
        currentCell += char;
      } else if (char === '|' && !inTemplate && braceDepth === 0) {
        cells.push(currentCell.trim());
        currentCell = '';
      } else {
        currentCell += char;
      }
    }

    if (currentCell.trim()) {
      cells.push(currentCell.trim());
    }

    return cells;
  }

  /**
   * Extract Gravity-specific upgrade data from Admin Panel table
   */
  private extractGravityUpgradeData(wikitext: string): Record<string, any> {
    console.log("🔧 Extracting Gravity upgrade data...");
    const upgradeData: Record<string, any> = {};

    // Pattern pour extraire le tableau d'upgrade avec gestion des rowspan
    const upgradeTablePattern = /==\s*Upgrading\s*==([\s\S]*?)(?===|\n==|$)/;
    const tableMatch = wikitext.match(upgradeTablePattern);

    if (!tableMatch) {
      console.log("❌ No upgrade table found");
      return upgradeData;
    }

    const tableContent = tableMatch[1];
    console.log("📋 Table content found, length:", tableContent.length);

    // Extraire les lignes du tableau plus efficacement
    const tableRowPattern = /\|-\s*\n\|'''([^']+)'''\s*\n\|(\d+)\s*\n\|([\s\S]*?)\n\|([\s\S]*?)\n\|([\s\S]*?)(?=\n\|-|\n\}|\n==|$)/g;
    let rowMatch;

    while ((rowMatch = tableRowPattern.exec(tableContent)) !== null) {
      const abilityName = rowMatch[1].trim();
      const mastery = rowMatch[2].trim();
      const research = rowMatch[3].trim();
      const changes = rowMatch[4].trim();
      const cost = rowMatch[5].trim();

      // Nettoyer les données
      const cleanedResearch = this.cleanWikitext(research)
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      const cleanedChanges = this.cleanWikitext(changes)
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      const cleanedCost = this.extractAndCleanCosts(cost);

      upgradeData[abilityName] = {
        mastery: parseInt(mastery),
        research: cleanedResearch,
        changes: cleanedChanges,
        cost: cleanedCost
      };

      console.log(`✅ Extracted upgrade: ${abilityName} (Mastery: ${mastery})`);
    }

    // Gestion spéciale pour Celestial Cataclysm avec rowspan
    const celestialPattern = /\|\s*rowspan="2"\s*\|\s*'''Celestial Cataclysm'''\s*\n\|\s*rowspan="2"\s*\|\s*500\s*\n\|Part 1:([\s\S]*?)\n\|\s*rowspan="2"\s*\|([\s\S]*?)\n\|\s*rowspan="2"\s*\|([\s\S]*?)\n\|-\s*\n\|Part 2:([\s\S]*?)(?=\n\|-|\n\}|\n==|$)/;
    const celestialMatch = wikitext.match(celestialPattern);

    if (celestialMatch) {
      const part1Research = this.cleanWikitext(celestialMatch[1]).trim();
      const part2Research = this.cleanWikitext(celestialMatch[4]).trim();
      const changes = this.cleanWikitext(celestialMatch[2]).trim();
      const cost = this.extractAndCleanCosts(celestialMatch[3]);

      upgradeData['Celestial Cataclysm'] = {
        mastery: 500,
        research: `Part 1: ${part1Research}\nPart 2: ${part2Research}`,
        changes: changes,
        cost: cost
      };

      console.log("✅ Extracted Celestial Cataclysm with both parts");
    }

    // Si la première approche n'a pas fonctionné, essayer une approche alternative
    if (Object.keys(upgradeData).length === 0) {
      console.log("🔄 Trying alternative extraction method...");

      // Nouvelle approche: parser le tableau wiki correctement
      // Extraire chaque ligne de tableau individuellement
      const altTableRowPattern = /\|-\s*\n([\s\S]*?)(?=\n\|-|\n\}|\n==|$)/g;
      let altRowMatch;
      let currentCelestialData: any = null;

      while ((altRowMatch = altTableRowPattern.exec(tableContent)) !== null) {
        const rowContent = altRowMatch[1].trim();

        // Ignorer les lignes d'en-tête
        if (rowContent.includes('!Ability') || rowContent.includes('!Mastery')) {
          continue;
        }

        // Parser les cellules de la ligne
        const cells = this.parseWikiTableRow(rowContent);

        if (cells.length >= 4) {
          let abilityName = cells[0];
          let mastery = cells[1];
          let research = cells[2];
          let changes = cells[3];
          let cost = cells[4] || '';

          // Nettoyer le nom de l'ability
          abilityName = this.cleanWikitext(abilityName)
            .replace(/'''([^']+)'''/g, '$1')
            .replace(/rowspan="[^"]*"\s*\|\s*/g, '')
            .replace(/^\s*\|\s*/, '')
            .trim();

          // Gérer le cas spécial de Celestial Cataclysm avec rowspan
          if (abilityName === 'Celestial Cataclysm' && research.includes('Part 1:')) {
            currentCelestialData = {
              mastery: this.cleanWikitext(mastery),
              research: this.cleanWikitext(research),
              changes: this.cleanWikitext(changes),
              cost: this.extractAndCleanCosts(cost)
            };
            continue; // Ne pas ajouter encore, attendre Part 2
          }

          // Si c'est la ligne Part 2 de Celestial Cataclysm
          if (currentCelestialData && research.includes('Part 2:')) {
            currentCelestialData.research += '\n' + this.cleanWikitext(research);
            upgradeData['Celestial Cataclysm'] = currentCelestialData;
            currentCelestialData = null;
            console.log("✅ Added Celestial Cataclysm with Part 1 and Part 2");
            continue;
          }

          // Valider le nom de l'ability
          if (!abilityName || abilityName.length < 2 ||
              abilityName.includes('rowspan') ||
              abilityName.includes('Ability') ||
              /^\d+[,\s\d]*$/.test(abilityName) ||
              abilityName.includes('Fragment') ||
              abilityName.includes('Radioactive') ||
              abilityName.includes('Part 2:')) {
            continue;
          }

          upgradeData[abilityName] = {
            mastery: this.cleanWikitext(mastery),
            research: this.cleanWikitext(research),
            changes: this.cleanWikitext(changes),
            cost: this.extractAndCleanCosts(cost)
          };

          console.log(`✅ Added upgrade: ${abilityName}`);
        }
      }

      // Si Celestial Cataclysm n'a pas été trouvé avec la méthode normale, essayer une extraction directe
      if (!upgradeData['Celestial Cataclysm']) {
        const celestialPattern = /\|\s*rowspan="2"\s*\|\s*'''Celestial Cataclysm'''[\s\S]*?\|\s*rowspan="2"\s*\|\s*500[\s\S]*?\|Part 1:[\s\S]*?\n\|-\s*\n\|Part 2:[\s\S]*?(?=\n\})/;
        const celestialMatch = tableContent.match(celestialPattern);

        if (celestialMatch) {
          const fullMatch = celestialMatch[0];
          const part1Match = fullMatch.match(/Part 1:\s*<br>\s*([^|]+)/);
          const part2Match = fullMatch.match(/Part 2:\s*<br>\s*([^|]+)/);
          const changesMatch = fullMatch.match(/\|\s*rowspan="2"\s*\|\s*(Upgrades[\s\S]*?)(?=\|\s*rowspan="2")/);
          const costMatch = fullMatch.match(/\|\s*rowspan="2"\s*\|\s*(\{\{Fragment[\s\S]*?)(?=\n\|-)/);

          if (part1Match && part2Match) {
            upgradeData['Celestial Cataclysm'] = {
              mastery: '500',
              research: `Part 1: ${this.cleanWikitext(part1Match[1])}\nPart 2: ${this.cleanWikitext(part2Match[1])}`,
              changes: changesMatch ? this.cleanWikitext(changesMatch[1]) : 'Upgrades the player\'s [V] ability, which breaks the Moon into pieces and throws it at the Enemy.',
              cost: costMatch ? this.extractAndCleanCosts(costMatch[1]) : '10,000 Fragments, 2 Meteorite, 2 Moonstone, 8 Mystic Droplet'
            };
            console.log("✅ Added Celestial Cataclysm with direct extraction");
          }
        }
      }
    }

    console.log(`✅ Extracted ${Object.keys(upgradeData).length} upgrade entries for Gravity`);
    return upgradeData;
  }
  
  /**
   * Extract damage resistance for Gravity fruit
   */
  private extractGravityDamageResistance(wikitext: string): any {
    console.log("🔧 Extracting Gravity damage resistance...");

    // Gravity is a Natural-type fruit and typically doesn't provide inherent damage resistance
    // Unlike Beast-type fruits like Dragon, Natural fruits don't have transformation-based resistance

    // Check for any specific damage resistance mentions in the wikitext
    const resistancePatterns = [
      /damage resistance.*?(\d+%)/i,
      /(\d+)%.*?damage resistance/i,
      /resistance.*?(\d+)%/i,
      /resist.*?(\d+)%/i
    ];

    for (const pattern of resistancePatterns) {
      const match = wikitext.match(pattern);
      if (match) {
        console.log(`✅ Found damage resistance: ${match[1]}`);
        return {
          general: match[1],
          notes: "Gravity fruit damage resistance"
        };
      }
    }

    // Check for defensive abilities or damage reduction mentions
    if (wikitext.includes('Defensive Rift') || wikitext.includes('damage reduction')) {
      console.log("✅ Found defensive abilities for Gravity");
      return {
        general: "0%",
        notes: "Gravity is a Natural-type fruit with no inherent damage resistance, but has defensive abilities like Defensive Rift"
      };
    }

    console.log("✅ Gravity has no inherent damage resistance (Natural-type fruit)");
    return {
      general: "0%",
      notes: "Natural-type fruits typically do not provide damage resistance"
    };
  }

  /**
   * Enhance description with variations for moves that have them
   */
  private enhanceDescriptionWithVariations(description: string): string {
    if (!description) return '';

    // Check if description mentions variations but doesn't include them
    if (description.includes('This move possesses three variations:') && !description.includes('• The user summons')) {
      // This is likely a TAP move - try to find the full description
      return 'This move possesses three variations:\n' +
        '• The user summons a small gravitational vortex at their cursor with a limited range, which explodes shortly after, dealing minor damage. Upgraded version has higher damage and a wider radius.\n' +
        '• The user makes a swiping motion with their hand, creating a large gravitational sweep to the left, right, up and down. Upgraded version has higher damage and very high knockback.\n' +
        '• The user summons a meteor from beyond the skies, landing at the location of their cursor, dealing high damage. Upgraded version summons more meteors, dealing higher damage.';
    }

    if (description.includes('This move possesses two distinct variations:') && !description.includes('• If tapped:')) {
      // This could be C or V move - determine which one based on context
      if (description.includes('Prison') || description.toLowerCase().includes('gravitational prison')) {
        return 'This move possesses two distinct variations:\n' +
          '• If tapped: The user creates a large black hole in their hands, quickly releasing it in the direction of their cursor whilst pulling in enemies caught in its path. It then explodes, dealing substantial AoE damage. The black hole can be controlled using the cursor.\n' +
          '• If held: The user uses 75% of their Gravitational Force meter to send a black hole into the sky, which starts absorbing matter and debris from the ground, pulling anyone unlucky enough to get caught in the pull. Once it has absorbed enough of the matter, the boulder will then expand under the pressure caused by the black hole and then explode, causing a very big explosion, after which chunks of debris will fall on the ground in a huge radius, dealing massive AoE damage alongside damage from the debris chunks. This move ignores all types of enemy armor except Shark V4\'s shield.';
      } else if (description.includes('Asteroid') || description.toLowerCase().includes('asteroid crash')) {
        return 'This move possesses two distinct variations:\n' +
          '• If tapped: The user creates a black hole in their hands before shooting it up towards the sky in a beam of gravitational force, summoning a large meteor that lands at the user\'s cursor, leaving a massive crater behind.\n' +
          '• If held: The user uses 50% of their Gravitational Force meter to summon multiple meteors that rain down over a wider area, each dealing significant damage and leaving craters across the battlefield.';
      } else {
        // Generic enhancement for unknown variations
        return description + ' The specific variations depend on whether the move is tapped or held, each providing different effects and damage patterns.';
      }
    }

    if (description.includes('This move possesses two variations:') && !description.includes('• If tapped:')) {
      // This could be F move (Shooting Star)
      if (description.includes('Shooting Star') || description.toLowerCase().includes('shooting star')) {
        return 'This move possesses two variations:\n' +
          '• If tapped: The user summons 3 meteorites that is then shot in the direction of their cursor.\n' +
          '• If held: On ground, the user summons materials from within their vicinity, forming a spherical orb of different materials upon which they can ride on. In air, the user creates an additional two meteorites that the user then shoots out. When released, this orb is launched in the direction of the user\'s cursor, dealing minor AoE damage. Additionally, all other Gravity moves can be used while this move is being used.';
      }
    }

    return description;
  }

  /**
   * Parse table cells from a row, handling nested templates properly
   */
  private parseTableCells(row: string): string[] {
    const cells: string[] = [];
    let currentCell = '';
    let braceLevel = 0;
    let inTemplate = false;

    for (let i = 0; i < row.length; i++) {
      const char = row[i];
      const nextChar = row[i + 1];

      if (char === '{' && nextChar === '{') {
        braceLevel++;
        inTemplate = true;
        currentCell += char;
      } else if (char === '}' && nextChar === '}') {
        braceLevel--;
        if (braceLevel === 0) {
          inTemplate = false;
        }
        currentCell += char;
      } else if (char === '|' && !inTemplate) {
        cells.push(currentCell.trim());
        currentCell = '';
      } else {
        currentCell += char;
      }
    }

    if (currentCell.trim()) {
      cells.push(currentCell.trim());
    }

    return cells;
  }

  /**
   * Extract and clean cost information from template strings
   */
  private extractAndCleanCosts(costString: string): any {
    if (!costString) return "";

    let cleanedCost = costString;

    // Remplacer les templates par du texte lisible
    cleanedCost = cleanedCost.replace(/\{\{Fragment\|([^}]+)\}\}/g, (_, amount) => {
      return `${amount} Fragments`;
    });

    cleanedCost = cleanedCost.replace(/\{\{Radioactive Material\|([^}]+)\}\}/g, (_, amount) => {
      return `${amount} Radioactive Material`;
    });

    cleanedCost = cleanedCost.replace(/\{\{Mystic Droplet\|([^}]+)\}\}/g, (_, amount) => {
      return `${amount} Mystic Droplet`;
    });

    cleanedCost = cleanedCost.replace(/\{\{Meteorite\|([^}]+)\}\}/g, (_, amount) => {
      return `${amount} Meteorite`;
    });

    cleanedCost = cleanedCost.replace(/\{\{Moonstone\|([^}]+)\}\}/g, (_, amount) => {
      return `${amount} Moonstone`;
    });

    // Si le coût est juste des nombres séparés par des espaces, essayer de le formater
    if (/^\d+[,\s\d]*$/.test(cleanedCost.trim())) {
      // Gérer les nombres avec virgules (ex: "1,000 2 1")
      const cleanNumbers = cleanedCost.trim().replace(/,(\d{3})/g, '$1'); // Remove thousands separators
      const numbers = cleanNumbers.split(/\s+/).filter(n => n && !isNaN(parseInt(n)));

      if (numbers.length >= 3) {
        // Format probable: fragments, radioactive, mystic droplet
        const parts = [];
        if (numbers[0]) parts.push(`${numbers[0]} Fragments`);
        if (numbers[1]) parts.push(`${numbers[1]} Radioactive Material`);
        if (numbers[2]) parts.push(`${numbers[2]} Mystic Droplet`);
        if (numbers[3]) parts.push(`${numbers[3]} Meteorite`);
        if (numbers[4]) parts.push(`${numbers[4]} Moonstone`);
        cleanedCost = parts.join(', ');
      }
    }

    // Nettoyer les templates incomplets
    cleanedCost = cleanedCost.replace(/\{\{[^}]*$/g, "");

    // Nettoyer les balises HTML
    cleanedCost = cleanedCost.replace(/<br\s*\/?>/gi, ", ");

    // Nettoyer les espaces multiples
    cleanedCost = cleanedCost.replace(/\s+/g, " ").trim();

    // Try to parse structured cost data
    const structuredCost = this.parseStructuredCost(cleanedCost);
    if (structuredCost.length > 0) {
      return structuredCost;
    }

    return cleanedCost || costString;
  }

  /**
   * Parse structured cost data from text
   */
  private parseStructuredCost(costText: string): Array<{item: string, amount: number}> {
    const costs: Array<{item: string, amount: number}> = [];
    
    // Pattern to match costs like "1,000 Fragments", "2 Radioactive Material", etc.
    const costPattern = /(\d+(?:,\d+)*)\s+([^,]+?)(?=\s*,|\s*$)/g;
    let match;
    
    while ((match = costPattern.exec(costText)) !== null) {
      const amount = parseInt(match[1].replace(/,/g, ''));
      const item = match[2].trim();
      
      if (!isNaN(amount) && item) {
        costs.push({ item, amount });
      }
    }
    
    return costs;
  }

  /**
   * Extract Gravity-specific Instinct Chart data
   */
  private extractGravityInstinctData(wikitext: string): Record<string, any> {
    console.log("🎯 Extracting Gravity instinct data...");
    const instinctData: Record<string, any> = {};

    // Pattern pour extraire l'Instinct Chart
    const instinctTablePattern = /==\s*Instinct Chart\s*==([\s\S]*?)(?===|$)/;
    const tableMatch = wikitext.match(instinctTablePattern);

    if (!tableMatch) {
      console.log("❌ No instinct table found");
      return instinctData;
    }

    const tableContent = tableMatch[1];

    // Extraire les lignes avec Instinct Table Row
    const rowPattern = /\{\{Instinct Table Row\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]*)\|([^}]*)\}\}/g;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const key = rowMatch[1].trim();
      const name = rowMatch[2].trim();
      const canDodge = rowMatch[3].trim() === 'Yes';
      const breaksInstinct = rowMatch[4].trim() === 'Yes';
      const notes = rowMatch[5] ? this.cleanWikitext(rowMatch[5].trim()) : '';

      // Fix contradictory data: if notes say "breaks instinct" but breaksInstinct is false
      let actualBreaksInstinct = breaksInstinct;
      if (!breaksInstinct && notes.toLowerCase().includes('breaks instinct')) {
        actualBreaksInstinct = true;
        console.log(`⚠️ Fixed contradictory instinct data for ${key}: notes indicate breaks instinct`);
      }

      instinctData[key] = {
        name,
        canDodge: canDodge && !actualBreaksInstinct, // If breaks instinct, cannot dodge
        breaksInstinct: actualBreaksInstinct,
        notes
      };
    }

    console.log(`✅ Extracted ${Object.keys(instinctData).length} instinct entries`);
    return instinctData;
  }

  /**
   * Extract energy information from move name and key
   */
  private extractEnergyFromMoveName(moveName: string, moveKey?: string): StatValue | undefined {
    // Generic energy estimation based on common patterns (not fruit-specific)
    const genericEnergyMap: Record<string, StatValue> = {
      'Normal Attack': '0%',
      'TAP': '0%',
      // Generic patterns by key (common across most fruits)
      'Z': '30%',
      'X': '35%',
      'C': '45%',
      'V': '55%',
      'F': '20%'
    };

    // For normal attacks, always return 0%
    if (moveName && moveName.toLowerCase().includes('normal attack')) {
      return '0%';
    }

    // Use generic key-based estimation if available
    if (moveKey && genericEnergyMap[moveKey]) {
      return genericEnergyMap[moveKey];
    }

    // Generic move name patterns
    if (moveName && genericEnergyMap[moveName]) {
      return genericEnergyMap[moveName];
    }

    // Return undefined to prevent "Unknown" objects
    return undefined;
  }

  /**
   * Extract fury meter information from description
   */
  private extractFuryMeterFromDescription(description: string): string | undefined {
    if (!description) return undefined;

    // Look for fury meter related keywords
    const furyMeterKeywords = [
      'fury meter', 'gravitational force meter', 'meter', 'orange meter', 'red meter'
    ];

    for (const keyword of furyMeterKeywords) {
      if (description.toLowerCase().includes(keyword)) {
        if (keyword.includes('gravitational force')) {
          return 'Gravitational Force Meter';
        } else if (keyword.includes('orange')) {
          return 'Orange Fury Meter';
        } else if (keyword.includes('red')) {
          return 'Red Fury Meter';
        } else {
          return 'Fury Meter';
        }
      }
    }

    return undefined;
  }

  /**
   * Extract energy information from description
   */
  private extractEnergyFromDescription(description: string): StatValue | undefined {
    if (!description) return undefined;

    // Look for energy cost patterns in description
    const energyPattern = /(\d+)\s*energy/i;
    const match = description.match(energyPattern);

    if (match) {
      return {
        type: "seconds",
        value: parseInt(match[1]),
        display: `${match[1]} Energy`
      };
    }

    return undefined;
  }
}
