export interface RateLimiter {
  requests: number[]
  maxRequests: number
  timeWindow: number
}

export interface Move {
  name: string
  key?: string
  damage?: number
  cooldown?: number
  mastery?: number
  energy?: number
  type?: string
  description?: string
}

export interface MaterialData {
  berryTypes?: string[]
  locations?: Array<{ sea: string; location: string; bushes: number }>
  usage?: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }>
  totalRequired?: Record<string, number>
  maxStack?: number
  source?: string
  spawnRate?: string
  despawnTime?: string
}

export interface PriceData {
  current: {
    money?: number
    robux?: number
    status?: "AVAILABLE" | "UNAVAILABLE" | "IN_PROGRESS"
  }
  historical?: Array<{
    money?: number
    robux?: number
    update?: string
    context?: string
  }>
  discounts?: Array<{
    originalPrice: number
    discountedPrice: number
    type: "money" | "robux"
    context?: string
  }>
  sources?: string[]
}

export type StatValue = number | string | null | {
  type: string;
  reason?: string;
  display: string; // Always clean text without HTML
  note?: string;
  variants?: any[];
  min?: number;
  max?: number;
  unit?: string;
  average?: number;
  value?: number;
};

export interface FruitForm {
  name: string
  type: "Normal" | "Hybrid" | "Transformed"
  variant?: string // For East/West variants
  moves: Array<{
    key: string
    name: string
    description: string
    mastery?: number
    damage?: StatValue
    cooldown?: StatValue
    energy?: StatValue
    furyMeter?: string
    gif?: string
    gif1?: string // Additional GIF variants
    gif2?: string
    damageRaw?: string
    cooldownRaw?: string
    energyRaw?: string
    furyMeterRaw?: string
  }>
  stats?: {
    damageResistance?: string
    movementSpeed?: string
    specialAbilities?: string[]
  }
  pros?: string[]
  cons?: string[]
  images?: string[]
}

export interface MeterDetail {
  fillMethod: "time-based" | "damage-based" | "hybrid";
  requiredFor: string;
  drainTriggers: string[];
  description?: string;
  requirements?: Record<string, string>; // e.g., {"C_ultimate": "75%", "V_ultimate": "100%"}
}

export interface FuryMeterMechanics {
  orangeMeter?: MeterDetail;
  redMeter?: MeterDetail;
  furyMeter?: MeterDetail; // Generic fury meter
  gravitationalForce?: MeterDetail; // For Gravity fruit
  passiveDrain?: boolean;
  allowsIndefiniteTransformation?: boolean;
}

// Generic meter mechanics type for any fruit
export type GenericMeterMechanics = Record<string, MeterDetail>;

export interface DamageResistanceData {
  human: string;
  hybrid?: {
    players: string;
    npcs: string;
  };
  transformed?: string;
}

export interface SkinSystem {
  defaultSkin: string;
  craftableSkins: string[];
  chromaticSkins: string[];
  acquisition: {
    crafting?: string;
    purchase?: string;
    limited?: string;
  };
  prices?: Record<string, number>;
}

export interface VariantData {
  eastern?: {
    name: string;
    theme: string;
    mountingCapacity?: number;
    specialMechanics: string[];
    movementStyle: string;
    culturalReference?: string;
  };
  western?: {
    name: string;
    theme: string;
    mountingCapacity?: number;
    specialMechanics: string[];
    movementStyle: string;
    flightSpeed?: string;
    culturalReference?: string;
  };
}

export interface EconomicData {
  acquisitionMethods: Array<{
    source: string;
    location?: string;
    chance?: string;
    type?: string;
  }>;
  reworkHistory: {
    majorUpdate?: string;
    tokenSystem?: string;
    priceIncrease?: {
      money?: string;
      robux?: string;
    };
  };
  competitiveRanking: {
    difficulty?: string;
    grinding?: string;
    pvp?: string;
  };
  marketAnalysis: {
    pricePosition?: string;
    availability?: string;
  };
}

export interface ChangeHistoryAdvanced {
  update: string;
  changes: Array<{
    description: string;
    type: "buff" | "nerf" | "addition" | "fix" | "general";
  }>;
  type: "major_rework" | "balance" | "release" | "general";
}

export interface FruitData {
  shopQuote?: string;
  awakening?: boolean;
  awakeningRequirements?: string[];
  type?: "Natural" | "Elemental" | "Beast" | "Ancient Zoan" | "Mythical Zoan" | "Zoan" | "Logia" | "Paramecia";
  rarity?: "Common" | "Uncommon" | "Rare" | "Legendary" | "Mythical";
  value?: number;
  stockChance?: number;
  spawnChance?: number;
  transformation?: boolean;
  transformationRequirements?: string[];
  m1Capability?: boolean;
  introducedUpdate?: string;
  mainDescription?: string;
  
  // NOUVEAU: Enhanced data structures
  furyMeterMechanics?: FuryMeterMechanics;
  meterMechanics?: GenericMeterMechanics; // Generic meter mechanics (fury, gravitational force, etc.)
  damageResistance?: DamageResistanceData;
  skinSystem?: SkinSystem;
  variantsComplete?: VariantData;
  economicData?: EconomicData;
  changeHistoryAdvanced?: ChangeHistoryAdvanced[];
  
  instinctData?: Record<string, any>;
  upgradeData?: Record<string, any>;
  variantData?: any;
  formSpecific?: Record<string, { pros: string[]; cons: string[] }>;
  
  // Legacy fields for compatibility
  forms?: FruitForm[];
  passiveAbilities?: Array<{ name: string; description: string; showcaseUrl?: string }>;
  masteryRequirements?: Record<string, number>;
  combatRating?: {
    pvp?: "Excellent" | "Good" | "Average" | "Poor";
    grinding?: "Excellent" | "Good" | "Average" | "Poor";
    raids?: "Excellent" | "Good" | "Average" | "Poor";
  };
  trivia?: string[];
  recommendations?: string[];
  counters?: string[];
  changeHistory?: Array<{ update: string; changes: string[] }>;
  priceData?: PriceData;
  upgrades?: Array<{
    name: string;
    mastery: number;
    research: string;
    changes: string;
    cost: {
      fragments?: number;
      materials?: Record<string, number>;
    }
  }>;
  gallery?: Array<{ url: string; caption: string }>;
  skins?: Array<{
    name: string;
    type: "Default" | "Craftable" | "Chromatic" | "Limited";
    price?: number;
    requirements?: string[];
  }>;
  detailedMoveStats?: Record<
    string,                       // Forme
    Record<
      string,                     // Nom du coup
      Array<{
        damage?: string
        cooldown?: string
        energy?: string
        furyMeter?: string
        description?: string
      }>
    >
  >
  statusEffects?: Array<{
    name: string;
    duration?: string;
    damage?: string;
    description: string;
  }>;
}

export interface WeaponData {
  damage?: number
  masteryRequired?: number
  upgradeRequirements?: Array<{ material: string; quantity: number }>
  specialAbilities?: string[]
  weaponType?: "Sword" | "Gun"
  version?: number
  dropChance?: number
  price?: number
  levelRequirement?: number
  stats?: Array<{ move: string; damage: number; cooldown: number }>
  pros?: string[]
  cons?: string[]
}

export interface AccessoryData {
  buffs?: Array<{ type: string; value: string; description: string }>
  rarity?: string
  dropSource?: string
  dropChance?: number
  location?: string
  price?: string
  pros?: string[]
  cons?: string[]
  stacksWith?: string[]
  trivia?: string[]
}

export interface MechanicData {
  purpose?: string
  triggeredBy?: string
  mechanics?: string[]
  notes?: string[]
  restrictions?: string[]
  trivia?: string[]
}

export interface NPCData {
  npcType?: "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
  sea?: number
  location?: string
  questRequirements?: Array<{ type: string; description: string; amount?: number }>
  questRewards?: Array<{ type: string; description: string; amount?: number }>
  questSteps?: string[]
  dialogue?: string[]
  cost?: number
  services?: string[]
}

export interface QuestData {
  questGiver?: string
  requirements?: Array<{ type: string; description: string; amount?: number }>
  rewards?: Array<{ type: string; description: string; amount?: number }>
  steps?: string[]
  difficulty?: "Easy" | "Medium" | "Hard" | "Extreme"
  estimatedTime?: string
  tips?: string[]
}

export interface EnemyData {
  enemyType?: "Raid" | "Boss" | "Regular" | "Elite"
  hp?: number
  level?: number
  baseAttack?: number
  attacks?: Array<{ name: string; description: string; howToAvoid: string }>
  immunity?: string[]
  aura?: boolean
  weapon?: string
  spawnLocation?: string[]
  behavior?: string
}

export interface RawData {
  infobox?: Record<string, string>
  movesRaw?: string
  descriptionRaw?: string
  fullWikitextSample?: string
  wikitextLength: number
  movesFound: number
  statsFound: number
  extractedAt: string
  materialData?: any
  fruitData?: any
  weaponData?: any
  accessoryData?: any
  mechanicData?: any
  npcData?: any
  questData?: any
  enemyData?: any
}

export interface ScrapedItem {
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  robuxPrice?: string
  priceStatus?: "AVAILABLE" | "UNAVAILABLE" | "IN_PROGRESS"
  description?: string
  stats?: string[]
  moves?: Move[]
  imageUrl?: string
  imageUrls?: string[]
  wikiUrl: string
  lastUpdated: Date
  obtainment?: string
  upgrading?: string
  location?: string
  requirements?: string[]
  rewards?: string[]
  hp?: number
  level?: number
  materialData?: MaterialData
  fruitData?: FruitData
  weaponData?: WeaponData
  accessoryData?: AccessoryData
  mechanicData?: MechanicData
  npcData?: NPCData
  questData?: QuestData
  enemyData?: EnemyData
  rawData: RawData
}

export interface CategoryConfig {
  name: string
  type: string
  collection: string
}

export interface ScraperConfig {
  mongoUrl: string
  baseUrl?: string
  rateLimiter?: {
    maxRequests?: number
    timeWindow?: number
  }
}
