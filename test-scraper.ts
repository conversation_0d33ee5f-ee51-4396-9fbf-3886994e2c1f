import { EnhancedFruitScraper } from './lib/scrapers/enhanced-fruit-scraper'

async function testScraper() {
  console.log("🧪 Testing enhanced scraper improvements...")

  const scraper = new EnhancedFruitScraper()

  try {
    // Test Dragon
    console.log('\n=== Testing Dragon ===')
    const dragonData = await scraper.scrapeItem('Dragon', 'fruit')
    if (dragonData) {
      console.log('✅ Dragon scraped successfully')
      console.log('- Name:', dragonData.name)
      console.log('- Category:', dragonData.category)
      console.log('- Type:', dragonData.type)
      console.log('- Robux price:', dragonData.fruitData?.priceData?.current?.robux)
      console.log('- Money price:', dragonData.fruitData?.priceData?.current?.money)
      console.log('- Upgrade data:', dragonData.fruitData?.upgradeData ? 'Present' : 'Missing')
      console.log('- Instinct data:', dragonData.fruitData?.instinctData ? 'Present' : 'Missing')
      console.log('- Forms count:', dragonData.fruitData?.forms?.length || 0)
      console.log('- Passive abilities:', dragonData.fruitData?.passiveAbilities?.length || 0)
      console.log('- Fury meter mechanics:', dragonData.fruitData?.furyMeterMechanics ? 'Present' : 'Missing')
      console.log('- Meter mechanics:', dragonData.fruitData?.meterMechanics ? 'Present' : 'Missing')
    } else {
      console.log('❌ Dragon scraping failed')
    }

    // Test Gravity
    console.log('\n=== Testing Gravity ===')
    const gravityData = await scraper.scrapeItem('Gravity', 'fruit')
    if (gravityData) {
      console.log('✅ Gravity scraped successfully')
      console.log('- Name:', gravityData.name)
      console.log('- Category:', gravityData.category)
      console.log('- Type:', gravityData.type)
      console.log('- Robux price:', gravityData.fruitData?.priceData?.current?.robux)
      console.log('- Money price:', gravityData.fruitData?.priceData?.current?.money)
      console.log('- Upgrade data:', gravityData.fruitData?.upgradeData ?
        Object.keys(gravityData.fruitData.upgradeData).length + ' entries' : 'Missing')
      console.log('- Instinct data:', gravityData.fruitData?.instinctData ?
        Object.keys(gravityData.fruitData.instinctData).length + ' entries' : 'Missing')
      console.log('- Forms count:', gravityData.fruitData?.forms?.length || 0)
      console.log('- Passive abilities:', gravityData.fruitData?.passiveAbilities?.length || 0)
      console.log('- Fury meter mechanics:', gravityData.fruitData?.furyMeterMechanics ? 'Present' : 'Missing')
      console.log('- Meter mechanics:', gravityData.fruitData?.meterMechanics ? 'Present' : 'Missing')
    } else {
      console.log('❌ Gravity scraping failed')
    }

  } catch (error) {
    console.error('❌ Test failed:', error instanceof Error ? error.message : String(error))
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace')
  }
}

testScraper()
