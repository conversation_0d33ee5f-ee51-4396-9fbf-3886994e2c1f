{"_id": {"$oid": "687bf0f603c56f76ca1e7daf"}, "name": "Dragon", "category": "fruit", "fruitData": {"type": "Beast", "rarity": "Mythical", "introducedUpdate": "13", "m1Capability": true, "awakening": false, "transformation": true, "mainDescription": "Dragon allows the user to gain the power, and transform to a mythical Dragon. The user can transform either to an Eastern Dragon; resembling those from the East Asian culture, or the Western Dragon; resembling a medieval armored Dragon from European folklore. The user can also transform into a large, tall human-hybrid dragon that deals more massive fiery damage.", "combatRating": {"pvp": "Excellent", "grinding": "Excellent"}, "furyMeterMechanics": {"furyMeter": {"type": "fury", "fillMethod": "time-and-damage-based", "description": "<PERSON>er (first Fury Meter) is full and before the red Fury Meter (second Fury Meter) is full. Passive=", "requirements": []}, "powerMeter": {"type": "power", "fillMethod": "time-and-damage-based", "description": "Power Meter Fury Meter]]) is full and before the red Fury Meter (second Fury Meter) is full. Passive=", "requirements": []}}, "damageResistance": {"human": "0%", "hybrid": {"players": "20%", "npcs": "34%"}, "transformed": "53%"}, "skinSystem": {"defaultSkin": "Green", "craftableSkins": ["Orange", "Yellow", "Blue", "Red", "Purple", "Black"], "chromaticSkins": ["Blood Moon", "Eclipse", "Ember", "Phoenix Sky", "Violet Night"], "acquisition": {"crafting": "Barista NPC", "purchase": "<PERSON><PERSON> (permanent required)", "chromatic": "CHROMATIC bundle in shop"}}, "variantsComplete": {"eastern": {"name": "Eastern Dragon", "theme": "East Asian mythology", "mountingCapacity": 4, "specialMechanics": ["aerial maneuverability", "spinning attacks", "upward spirals"], "movementStyle": "aerial spins and upward spirals", "culturalReference": "Chinese dragon (long)", "description": "Resembles creatures commonly found in Chinese myths with enhanced aerial maneuverability"}, "western": {"name": "Western Dragon", "theme": "European medieval", "mountingCapacity": 5, "specialMechanics": ["claw attacks", "direct assault", "fastest flight speed"], "movementStyle": "direct combat approach", "culturalReference": "European folklore dragon (wyvern)", "description": "Resembles medieval armored dragons from European folklore with powerful direct attacks", "flightSpeed": "Fastest in game"}}, "economicData": {"acquisitionMethods": [{"source": "Blox Fruit Dealer"}, {"source": "Blox Fruit Gacha"}, {"source": "Shop"}, {"source": "Sea Event", "type": "Sea Event"}, {"source": "Shrine", "type": "Sea Event"}, {"source": "Island", "chance": "extremely low", "type": "Sea Event"}, {"source": "Egg", "description": "* The Dragon Fruit can also be obtained from the Dragon Eggs at the Prehistoric Island with an extremely low chance, being able to get either the East or West variants"}, {"source": "Token", "description": "(Although the damage is low) }} }} Upon the release of Update 24, the old Dragon fruits (physical and equipped) were converted into Dragon Tokens, which could be exchanged for a random fruit, (including Dragon East and West) when used"}], "reworkHistory": {"majorUpdate": "Update 24", "tokenSystem": "Dragon Token conversion system", "priceIncrease": {"robux": "92.3%"}}, "competitiveRanking": {"difficulty": "Highest in game", "grinding": "Good", "pvp": "S-tier when transformed"}, "marketAnalysis": {"pricePosition": "Most expensive fruit", "availability": "Extremely rare", "tradeValue": "Very High"}}, "changeHistoryAdvanced": [{"update": "26", "changes": [{"description": "Buffed the Fury Meter gain from landing skills on base or hybrid mode.", "type": "buff"}, {"description": "Nerfed Dragon with the changes seen below:", "type": "nerf"}, {"description": "* Reduced the fury meter gain from landing skills on transformation.", "type": "general"}, {"description": "* Reduced transformed damage resistance from 60% to 53%.", "type": "general"}, {"description": "* Increase cooldowns for all moves (+0.5 seconds on each move).", "type": "general"}, {"description": "* Reduced damage of all moves (-5% on each move).", "type": "general"}, {"description": "* Slightly decreased Western Dragon transformation's flight speed.", "type": "general"}], "type": "balance"}, {"update": "24", "changes": [{"description": "Dragon was remade available to purchase.", "type": "general"}, {"description": "Dragon was reworked into two versions: Western and Eastern.", "type": "general"}, {"description": "* Added Dragon's Hybrid Form.", "type": "addition"}, {"description": "* Added an M1 attack.", "type": "addition"}, {"description": "* [Z] Heatwave Beam was renamed to [Z] Heatwave Cannon.", "type": "general"}, {"description": "* [X] Draconic Claw was renamed to [X] Infernal Pincer.", "type": "general"}, {"description": "* [C] Fire Shower was renamed to [C] Scorching Downfall.", "type": "general"}, {"description": "* [V] Transformation was renamed to [V] Imperial Evolution.", "type": "general"}, {"description": "* [F] Dragon Flight was renamed to [F] Draconic Soar.", "type": "general"}], "type": "major_rework"}, {"update": "23", "changes": [{"description": "Dragon was made unavailable to purchase from the Blox Fruit Dealer and Shop.", "type": "general"}, {"description": "* Its price was changed to \"IN PROGRESS\".", "type": "general"}, {"description": "* It started getting reworked.", "type": "general"}], "type": "major_rework"}, {"update": "20", "changes": [{"description": "Dragon Fruit's model icon was remade.", "type": "general"}], "type": "general"}, {"update": "17.3", "changes": [{"description": "[C] Fire Shower and [X] Dragonic Claw were reworked.", "type": "general"}, {"description": "* Fire Shower's hitbox was reduced.", "type": "general"}, {"description": "* Both of these abilities got a small VFX rework.", "type": "general"}], "type": "major_rework"}], "forms": [{"name": "Normal", "type": "Normal", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user begins to scratch their opponent with fiery claws three times, each scratch with differing directions. This move is similar to the ones of <PERSON><PERSON><PERSON> and T-Rex.", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/84/Dragon_M1.gif/revision/latest?cb=20250603112848", "gif1": null, "gif2": null, "damage": "Low", "cooldown": null, "energy": {"type": "percentage", "value": 0, "display": "0%"}, "furyMeter": "<PERSON>"}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user fires a beam of scorching hot energy, knocking enemies back. Holding this move will make this move have more range and damage. It can be held for around 5-6 seconds. While using this attack, the user floats, so they won't fall.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/e9/Dragon_ZB.gif/revision/latest?cb=20241216204314", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/89/Dragon_ZA.gif/revision/latest?cb=20241216204314", "gif2": null, "damage": "High", "cooldown": 40, "energy": {"type": "percentage", "value": 4, "display": "4%"}, "furyMeter": "<PERSON>"}, {"key": "X", "name": "Infernal Pincer", "description": "When held, the user prepares themselves to launch towards their opponent. As the key is released, the user will dash at their opponent and drag them before full on throwing them at their cursor smashing into the ground. This move deals 2 ticks of burn afterwards. This move is similar to Eagle's [C] <PERSON>er, except the user can aim the slam.", "mastery": 150, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a3/Dragon_XA.gif/revision/latest?cb=20241217210604", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/e5/Dragon_XB.gif/revision/latest?cb=20241217210605", "gif2": null, "damage": "High", "cooldown": 60, "energy": {"type": "percentage", "value": 6, "display": "6%"}, "furyMeter": "<PERSON>", "effects": ["burning"]}, {"key": "C", "name": "Scorching Downfall", "description": "The user leaps into the air and begins bombarding the ground with fiery projectiles. Upon impact, the projectiles explode, damaging enemies caught in the vicinity, knocking them back, and stunning them for a brief moment. Holding this move allows the user to shoot fireballs for around 2.5 seconds. After an individual enemy reaches a certain threshold of damage from fireballs, they will take greatly reduced damage from fireballs. This threshold is reached after about half of the total fireballs hit the enemy, meaning the user can miss some of their fireballs or end their skill early and still deal most of the damage. This move is similar to unawakened Light's [X] move.", "mastery": 250, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/18/Dragon_C.gif/revision/latest?cb=20241216204310", "gif1": null, "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 100, "energy": {"type": "range", "min": 9, "max": 19, "unit": "%", "display": "9-19%", "average": 14}, "furyMeter": "<PERSON>", "effects": ["stun"]}, {"key": "V", "name": "Imperial Evolution", "description": "If the user has acquired all of the orange fury meter bar, they will proceed to evolve into hybrid form. But if the user has the red fury meter bar full, they will transform into a dragon depending on their choice of east or west. However, the user cannot use fighting styles, swords, and guns while transformed.", "mastery": 350, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/45/Dragon_FA.gif/revision/latest?cb=20241216204312", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a7/Dragon_VC.gif/revision/latest?cb=20241216204312", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2f/Dragon_VA.gif/revision/latest?cb=20241216204313", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 10, "energy": {"type": "conditional", "display": "15% (Hybrid) 30% (Full Form)", "variants": [{"value": "15%", "condition": "Hybrid", "display": "15% (Hybrid)"}, {"value": "30%", "condition": "Full Form", "display": "30% (Full Form)"}]}, "furyMeter": "<PERSON>"}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user grows dragon wings and lunges at their desired opponent. If the attack lands, the player will grab the opponent, damage them and dash away (the user can keep flying after hitting by holding the move). If not, they will be able to soar, hence the name. The user will fly faster when aiming downward.", "mastery": 75, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/9c/Dragon_VB.gif/revision/latest?cb=20241219192636", "gif1": null, "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d8/Dragon_Hybrid_M1.gif/revision/latest?cb=20250526185336", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 50, "energy": {"type": "percentage", "value": 2.5, "display": "2.5%"}, "furyMeter": "<PERSON>"}]}, {"name": "Hybrid Form", "type": "Hybrid", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user attacks their desired opponent with scorching scratches five times. The hybrid version's range is a lot better, and the slashes also set anyone hit on fire for a short period.", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b8/Dragon_Hybrid_ZA.gif/revision/latest?cb=20241216170623", "gif1": null, "gif2": null, "damage": "Low", "cooldown": null, "energy": {"type": "percentage", "value": 0, "display": "0%"}, "furyMeter": "<PERSON>"}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user violently fires a beam of scorching energy at their desired opponent (or their cursor), knocking enemies backward. If the key is held, the move becomes significantly more powerful, dealing even more damage and range.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/28/Dragon_Hybrid_ZB.gif/revision/latest?cb=20241216170625", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2b/Dragon_FB.gif/revision/latest?cb=20241216204312", "gif2": null, "damage": "High", "cooldown": 40, "energy": {"type": "percentage", "value": 4, "display": "4%"}, "furyMeter": "<PERSON>"}, {"key": "X", "name": "Infernal Pincer", "description": "When held, the user will prep themselves to dash. Upon release, the user (depending on if they land the attack or not) will lunge towards their opponent accompanied by a trail of flames and slam them at their cursor. This move deals 2 ticks of burn afterwards.", "mastery": 150, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/da/Dragon_Hybrid_XA.gif/revision/latest?cb=20241217210606", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/26/Dragon_Hybrid_C.gif/revision/latest?cb=20241216170621", "gif2": null, "damage": "High", "cooldown": 60, "energy": {"type": "percentage", "value": 6, "display": "6%"}, "furyMeter": "<PERSON>", "effects": ["burning"]}, {"key": "C", "name": "Scorching Downfall", "description": "The user leaps into the air and bombards the ground with multiple fiery projectiles. Upon impact, the projectiles explode, damaging enemies caught in the vicinity, knocking them back, and stunning them for a brief moment. Holding this move allows the user to shoot fireballs for around 4 seconds. After an individual enemy reaches a certain threshold of damage from fireballs, they will take greatly reduced damage from fireballs. This threshold is reached after about half of the total fireballs hit the enemy, meaning the user can miss some of their fireballs or end their skill early and still deal most of the damage. The hybrid version lets the user hold it for longer, meaning more damage can be applied.", "mastery": 250, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/78/Dragon_Hybrid_VA.gif/revision/latest?cb=20241216170623", "gif1": null, "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 100, "energy": {"type": "range", "min": 9, "max": 24, "unit": "%", "display": "9-24%", "average": 16.5}, "furyMeter": "<PERSON>", "effects": ["stun"]}, {"key": "V", "name": "Imperial Evolution", "description": "Depending on if the user's fury meter is full or not, the user will transform into either their human form or their Dragon form.", "mastery": 350, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f3/Dragon_Hybrid_XB.gif/revision/latest?cb=20241217210606", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/62/Dragon_Hybrid_VC.gif/revision/latest?cb=20241217213351", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/26/Dragon_Hybrid_VB.gif/revision/latest?cb=20241219192637", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 10, "energy": {"type": "conditional", "display": "0% (Normal) 30% (Full Form)", "variants": [{"value": "0%", "condition": "Normal", "display": "0% (Normal)"}, {"value": "30%", "condition": "Full Form", "display": "30% (Full Form)"}]}, "furyMeter": "<PERSON>"}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user sprouts out draconic wings out of their back and lunge forward. If the attack lands, the user will grab said person and slash them away (the user can keep flying after hitting by holding the move). If nobody is hit, they will be able to continue their soar. The user usually flies faster when aiming downward, and if the move hits the flight won't be interrupted.", "mastery": 75, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/66/Dragon_Hybrid_FA.gif/revision/latest?cb=20241216170623", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/41/Dragon_Hybrid_FB.gif/revision/latest?cb=20241216170623", "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 50, "energy": {"type": "percentage", "value": 2.5, "display": "2.5%"}, "furyMeter": "<PERSON>"}]}, {"name": "Transformed (East)", "type": "Transformed", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The player opens their mouth and begins to blast fire at their cursor for around 2 seconds before completely depleting. (Can be held for longer, but the range will decrease rapidly.)", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d0/Dragon_East_M1.gif/revision/latest?cb=20241219192635", "gif1": null, "gif2": null, "damage": "Low", "cooldown": null, "energy": {"type": "range", "min": 3, "max": 15.5, "unit": "%", "display": "3-15.5%", "average": 9.25}, "furyMeter": "<PERSON>"}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user, with great force, charges a beam from their mouth (depending on if the move is held) and will violently fire a beam of hot energy. Whereas if the beam is fully charged it causes the rings to glow purple. This move possesses an extreme range and big AoE. The user can move around while charging this move.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f6/Dragon_East_ZB.gif/revision/latest?cb=20241219192636", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/71/Dragon_East_ZA.gif/revision/latest?cb=20241219192637", "gif2": null, "damage": "High", "cooldown": 40, "energy": {"type": "range", "min": 9, "max": 39, "unit": "%", "display": "9-39%", "average": 24}, "furyMeter": "<PERSON>", "effects": ["aoe"]}, {"key": "X", "name": "Infernal Pincer", "description": "The user spins in an upward motion, grabbing anyone unlucky enough to be within vicinity. The user then dives downwards, causing a massive explosion and leaving debris everywhere.", "mastery": 150, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1e/Dragon_East_C.gif/revision/latest?cb=20241219192633", "gif1": null, "gif2": null, "damage": "High", "cooldown": 60, "energy": {"type": "percentage", "value": 15.5, "display": "15.5%"}, "furyMeter": "<PERSON>"}, {"key": "C", "name": "Scorching Downfall", "description": "The user spins upwards, albeit much slower and in a wider space than the previous move, during which a firestorm will accompany the user's spin. At the end, the user reaches peak altitude, and a purple explosion finishes off the move. The length of this move depends on how long it is held.", "mastery": 250, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/98/Dragon_East_VA.gif/revision/latest?cb=20241219192635", "gif1": null, "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 100, "energy": {"type": "range", "min": 17, "max": 25, "unit": "%", "display": "17-25%", "average": 21}, "furyMeter": "<PERSON>"}, {"key": "V", "name": "Imperial Evolution", "description": "The user turns back to their normal form (or hybrid if they were in that state before turning into a Dragon).", "mastery": 350, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/Dragon_East_FA.gif/revision/latest?cb=20241219192635", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5b/Dragon_East_VB.gif/revision/latest?cb=20241219192635", "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 10, "energy": {"type": "percentage", "value": 0, "display": "0%"}, "furyMeter": "<PERSON>"}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user aims at their cursor and begins spinning. The user gains speed aiming downward, and can even crash onto solid surfaces which causes an large, fiery explosion. Attempting to crash into the ocean will damage non-shark users, including the users themselves.", "mastery": 75, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/49/Dragon_East_X.gif/revision/latest?cb=20241219192635", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5d/Dragon_West_X.gif/revision/latest?cb=20241218001754", "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 50, "energy": {"type": "range", "min": 9, "max": 13.3, "unit": "%", "display": "9-13.3%", "average": 11.15}, "furyMeter": "<PERSON>"}]}, {"name": "Transformed (West)", "type": "Transformed", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user creates fire out of its mouth, and it lasts for around two seconds before running out. (Can be held for longer, but the range will decrease heavily.)", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f9/Dragon_West_M1.gif/revision/latest?cb=20241218001755", "gif1": null, "gif2": null, "damage": "Low", "cooldown": null, "energy": {"type": "range", "min": 3, "max": 15.5, "unit": "%", "display": "3-15.5%", "average": 9.25}, "furyMeter": "<PERSON>"}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user shoots a huge beam of fire from their mouth. When the move is fully charged it causes a ring of fire appear in front of the player's head. This move possesses a considerable range and big AoE. Unlike the East variant, the player is unable to move whilst charging this move.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/38/Dragon_West_ZB.gif/revision/latest?cb=20241218001756", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fe/Dragon_West_ZA.gif/revision/latest?cb=20241218001756", "gif2": null, "damage": "High", "cooldown": 40, "energy": {"type": "range", "min": 9, "max": 39, "unit": "%", "display": "9-39%", "average": 24}, "furyMeter": "<PERSON>", "effects": ["aoe"]}, {"key": "X", "name": "Infernal Pincer", "description": "The user uses their claws to create huge slashes, similar to the classic Dragon's and Sanguine Art's X move but a lot better in visuals, speed, AoE and damage.", "mastery": 150, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/19/Dragon_East_FB.gif/revision/latest?cb=20241219192635", "gif1": null, "gif2": null, "damage": "High", "cooldown": 60, "energy": {"type": "percentage", "value": 13.3, "display": "13.3%"}, "furyMeter": "<PERSON>", "effects": ["aoe"]}, {"key": "C", "name": "Scorching Downfall", "description": "The user shoots an orb which rains meteors downwards, doing very high damage and having an explosion at the end.", "mastery": 250, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/be/Dragon_West_C.gif/revision/latest?cb=20241218001752", "gif1": null, "gif2": null, "damage": "Very High", "cooldown": 100, "energy": {"type": "percentage", "value": 18, "display": "18%"}, "furyMeter": "<PERSON>"}, {"key": "V", "name": "Imperial Evolution", "description": "The user turns back to their normal avatar (or hybrid if they were in that state before turning into a Dragon).", "mastery": 350, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/63/Dragon_West_FB.gif/revision/latest?cb=20241218001754", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/3e/Dragon_West_VA.gif/revision/latest?cb=20241218001754", "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 10, "energy": {"type": "percentage", "value": 0, "display": "0%"}, "furyMeter": "<PERSON>"}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user picks up an opponent, slices them, throws them back to the ground, and then slams themselves onto them.", "mastery": 75, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/14/Dragon_West_VB.gif/revision/latest?cb=20241218001754", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/44/Dragon_West_FA.gif/revision/latest?cb=20241218001755", "gif2": null, "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 50, "energy": {"type": "percentage", "value": 9, "display": "9%"}, "furyMeter": "<PERSON>"}]}], "passiveAbilities": [{"name": "Draconic Dominance", "description": "Fury Meter * The user starts with a small \"[[Power Meter", "showcaseUrl": null}, {"name": "Draconic Blood * In Hybrid form, the user gains a 20% (34% against [[Enemies", "description": "NPCs]]) damage resistance to all attacks, causes a burning effect with M1 attacks, and has their moveset greatly enhanced.", "showcaseUrl": null}, {"name": "Draconic Monarch's Scales * In Dragon form, the user gains a 53% damage resistance to all attacks and is able to [[Flight", "description": "fly]]. The user can still be affected by crowd control but still able to move.", "showcaseUrl": null}, {"name": "Strong Hair/Saddle", "description": "Can be unlocked after getting 500 mastery on Dragon and talking to the Dragon Tamer NPC and paying . When transformed, the user can be mounted by other allied players. Maximum is 4 for Eastern Form and 5 for Western Form. (The purchase only applies to the currently equipped variant, and the user will have to make another purchase in the other variant for obtaining the passive on both variants.)", "showcaseUrl": null}, {"name": "Skins", "description": "The user can obtain other colors for their Dragon form by crafting them. They can also be bought using , but Permanent Dragon must be owned. Every owned color can be selected inside the inventory in the \"Skins\" category. The user can also use five chromatic colors which can be obtained from the bundle in the shop. * is unlocked and equipped by default. * The , , , , , and colors are unlocked crafting them with the [[Barista", "showcaseUrl": null}], "masteryRequirements": {"Heatwave Cannon": 1, "Infernal Pincer": 150, "Scorching Downfall": 250, "Imperial Evolution": 350, "Draconic Soar": 75}, "priceData": {"current": {"money": 15000000, "status": "AVAILABLE", "robux": 5000}, "sources": ["Blox Fruit Dealer", "Shop"], "historical": [{"money": 1999, "context": "Robux price", "type": "robux"}, {"money": 2400, "context": "Robux price", "type": "robux"}, {"money": 2600, "context": "Robux price", "type": "robux"}, {"money": 5000, "context": "Robux price", "type": "robux"}]}, "trivia": ["The Dragon Fruit can also be obtained from the Dragon Eggs at the Prehistoric Island with an extremely low chance, being able to get either the East or West variants.", "Dragon is the most expensive and difficult-to-obtain fruit in the game.", "Before Update 24, <PERSON> was the third most expensive fruit in the game, with the first being <PERSON><PERSON><PERSON> and the second being <PERSON><PERSON>.", "Before Update 17.3, Dragon was once again the most expensive fruit in the game, valued . This is the second time that Dragon is the most valuable fruit.", "Pre-rework, Dragon costed , which increased by 92.3% up to post-rework, making it by far the highest Robux price increase of any purchasable item in the game.", "* Similarly, it also has the highest Money price increase of any reworked item, going from to . According to the calculations, the price of that is increased by 328.57%.", "Mastery gained will be common to both Dragon variations.", "Dragon has the largest transformed model in the game.", "The transformed Eastern form dragon resembles a creature commonly found in Chinese myths, specifically a [https://en.wikipedia.org/wiki/Chinese_dragon long], whereas the Western represents a European folklore Dragon, specifically a [https://en.wikipedia.org/wiki/Wyvern wyvern]. Additionally, the Eastern Dragon is supposed to be the ruler of seas and bodies of water, unlike its Blox Fruits counterpart, whose moves are mostly fire-based.", "* Dragon's Western version is also similar to a fan-made Wyvern fruit showcased in the 2024 fan art competition.", "* Additionally, in most Chinese myths, Eastern Dragons are depicted to have control over the weather, thus allowing them to wield water, thunder and wind.", "<PERSON> and <PERSON><PERSON><PERSON> are the only beast fruits that, while transformed, can carry players on their backs. However, <PERSON> needs <PERSON><PERSON>/<PERSON> Hair to carry someone on their back.", "Dragon and Kitsune are the only fruits that can be obtained from Sea Events such as the Kitsune Shrine and Prehistoric Island.", "The user can measure how fast they are moving using the Dragon's Hybrid form. The higher the speed, the higher the volume and pitch of the footsteps of the hybrid.", "Dragon is the first fruit to have two versions on its transformation and fruit model.", "Dragon has the highest damage reduction in the game.", "Using a Race Awakening transformation while using the hybrid form drains the user's Fury Meter.", "Having the permanent version of <PERSON> allows the user to choose the version of the fruit used. (Either Western or Eastern.)", "Sometimes, a bug can happen with the Western Dragon where if the user untransforms, they will not be able to move. In the case of the Eastern Dragon, this bug causes players who untransformed to have a larger hitbox, allowing them to collect items such as fruits and chests much further than supposed and makes users sit in chairs frequently. This can be fixed by resetting the character or transforming into the hybrid form.", "Dragon is considered one of the best fruits for Leviathan hunt because the M1 hybrid form can deal insane damage. The reason it's better than Magma is because its ability is easier to target than moving targets like segment and limits knockback when spamming M1. However, for the head, Magma is still better.", "Interestingly, <PERSON>'s icon in the shop is the only icon that goes out of the margin.", "Dragon is the second fruit that has two icons, the other being Control.", "Dragon's untransformed has a similar look to Light's .", "The icon on inventory of the permanent dragon looks merged.", "<PERSON> is the first fruit in the Stock menu to have a unique plaque, being changed in Update 24.", "There is a glitch where discounted Dragon appears on the recommended fruits bar.", "There is a glitch where if the player transforms, they are sometimes \"invisible\" to other players.", "<PERSON> was the first fruit to receive the 'in-progress' status, followed by <PERSON><PERSON><PERSON> as the second, both <PERSON><PERSON> and <PERSON> as the third, <PERSON> as the fourth, and <PERSON><PERSON> as the fifth."], "gallery": [{"url": "latest", "caption": "Fruit Icon"}, {"url": "latest", "caption": "Icon"}, {"url": "latest", "caption": "Fruit Icon"}, {"url": "latest", "caption": "Fruit GIF"}, {"url": "latest", "caption": "Icon"}, {"url": "latest", "caption": "Fruit Icon"}, {"url": "latest", "caption": "Fruit GIF"}, {"url": "latest", "caption": "Icon"}, {"url": "latest", "caption": "Hybrid Form"}, {"url": "latest", "caption": "Eastern Dragon Form"}, {"url": "latest", "caption": "Eastern Dragon Form (Aura)"}, {"url": "latest", "caption": "Western Dragon Form"}, {"url": "latest", "caption": "Western Dragon Form (Aura)"}, {"url": "latest", "caption": "Eastern Dragon concept artist was created by @Tobey_D_Artist (on X/Twitter)."}, {"url": "latest", "caption": "Eastern dragon 3D model was created by @oreI_orL (on X/Twitter)."}, {"url": "latest", "caption": "Western dragon 3D model was created by @oreI_orL (on X/Twitter)."}, {"url": "latest", "caption": "Both Eastern and Western Dragons together."}, {"url": "latest", "caption": "Old Dragon's inventory icon."}, {"url": "latest", "caption": "Old Dragon's fruit icon."}, {"url": "latest", "caption": "Older Dragon Fruit's inventory icon."}], "shopQuote": "Transforms the user into a mighty Dragon, allowing them to rule over the skies with scorching flames.", "upgradeData": {}, "instinctData": {}, "formSpecific": {"Transformed (East)": {"pros": ["High damage reduction.", "Shorter cooldowns.", "Good grinding fruit.", "Alright for PvP.", "Smaller hitbox compared to the Western form.", "Can solo raids with enough time", "Can damage Sea Beast and <PERSON><PERSON><PERSON>.", "Continuous AoE attack.", "Highest range in the game if held.", "Large hitbox.", "High knockback.", "Applies burn damage over time.", "High stun that ensures the last hit always connects.", "Extremely large hitbox.", "Extremely high stun which drags enemies upwards.", "The user can control the duration of the move for combos, unlike the West version.", "When used with Cyborg/Ghoul V4, it will make the player get full/ almost full health.", "Decent damage.", "Gives the user a long i-frame.", "Good combo potential.", "Very fast."], "cons": ["Very large hitbox, which makes the user vulnerable.", "Good coverage, but poor range relative to the player's character.", "Cursed Dual Katana [X] ignores the damage reduction.", "Weakens if used for a long time, shortening the flame distance.", "Requires some time to use fully recharged flames.", "<PERSON><PERSON> can be dodged in aerial combat.", "Drastically drains the fury meter when charging it up.", "Lack of range.", "Forces the user to stay in animation.", "The damage area is very specific and misses most of the time.", "Less effective in aerial combat due to the slow flying speed and low spin damage.", "Hard to control when transformed", "Cannot be cancelled by damage."]}, "Transformed (West)": {"pros": ["High damage reduction.", "Shorter cooldowns.", "Good grinding fruit.", "Insanely good for PvP.", "Can solo every raid with enough experience, including <PERSON><PERSON>.", "Fastest flight speed in the game.", "Damages Sea Beasts.", "Continuous AoE attack.", "Highest range in the game if held.", "Large hitbox.", "High knockback.", "Long range.", "Very good burst damage.", "Applies burn damage over time.", "Extremely large hitbox.", "Quick start-up.", "Can grab enemies in the middle of the attack.", "Aimable, unlike the East version.", "Decent damage and knockback.", "Large AoE.", "Good combo potential.", "Great mobility."], "cons": ["Very large hitbox.", "Cursed Dual Katana [X] partially ignores the damage reduction.", "Although the fastest speed, it still relies on the [F] due to its low sustained speed when flying upwards.", "Weakens if used for long time, shortening flame distance.", "Requires some time to use fully recharged flames.", "<PERSON><PERSON> can be dodged in aerial combat.", "Drastically drains the fury meter when charging it up.", "No general cons to speak of.", "The orbs are often shot too high and do not attract enemies.", "Drains the fury meter when holding down the move.", "Missing the move drains a significant amount of fury meter.", "Requires a solid surface to deal full damage.", "Goes downward after grabbing an enemy, which can make the user drown if used above water. (Although the damage is low)"]}}}, "imageUrl": "Dragon Fruit.png", "imageUrls": ["https://static.wikia.nocookie.net/roblox-blox-piece/images/1/18/Dragon_C.gif/revision/latest?cb=20241216204310", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/84/Dragon_M1.gif/revision/latest?cb=20250603112848", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/89/Dragon_ZA.gif/revision/latest?cb=20241216204314", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a3/Dragon_XA.gif/revision/latest?cb=20241217210604", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/e2/DragonMeter.gif/revision/latest?cb=20250102193554", "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d8/Dragon_Hybrid_M1.gif/revision/latest?cb=20250526185336", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b8/Dragon_Hybrid_ZA.gif/revision/latest?cb=20241216170623", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/45/Dragon_FA.gif/revision/latest?cb=20241216204312", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2f/Dragon_VA.gif/revision/latest?cb=20241216204313", "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/da/Dragon_Hybrid_XA.gif/revision/latest?cb=20241217210606", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/26/Dragon_Hybrid_C.gif/revision/latest?cb=20241216170621", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/66/Dragon_Hybrid_FA.gif/revision/latest?cb=20241216170623", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/78/Dragon_Hybrid_VA.gif/revision/latest?cb=20241216170623", "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d0/Dragon_East_M1.gif/revision/latest?cb=20241219192635", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/71/Dragon_East_ZA.gif/revision/latest?cb=20241219192637", "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f9/Dragon_West_M1.gif/revision/latest?cb=20241218001755", "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1e/Dragon_East_C.gif/revision/latest?cb=20241219192633", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/98/Dragon_East_VA.gif/revision/latest?cb=20241219192635", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/Dragon_East_FA.gif/revision/latest?cb=20241219192635", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/49/Dragon_East_X.gif/revision/latest?cb=20241219192635", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/be/Dragon_West_C.gif/revision/latest?cb=20241218001752", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/3e/Dragon_West_VA.gif/revision/latest?cb=20241218001754", "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5d/Dragon_West_X.gif/revision/latest?cb=20241218001754", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/44/Dragon_West_FA.gif/revision/latest?cb=20241218001755", "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fe/Dragon_West_ZA.gif/revision/latest?cb=20241218001756", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/29/Dragon_Fruit.png/revision/latest?cb=20241218114129", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/37/Dragon_%28East%29.png/revision/latest?cb=20241231011003", "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c7/Dragon.png/revision/latest?cb=20241231011212", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/4b/Dragon_%28East%29_Fruit.png/revision/latest?cb=20241222155214", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/ec/Dragon_%28East%29Fruit_Gif.gif/revision/latest?cb=20241221193345", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/aa/Dragon_%28West%29.png/revision/latest?cb=20241215200412", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/40/Dragon_%28West%29_Fruit.png/revision/latest?cb=20241222155217", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/bb/Dragonwest.gif/revision/latest?cb=20250713110156", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8a/Hybrid_Transform.png/revision/latest?cb=20250211183633", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/97/DragonEast38242342.png/revision/latest?cb=20250211184544", "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/08/RedgreenandblueEastern.jpg/revision/latest?cb=20250106070152", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6b/East_Dragon_Concept_Artist.jpeg/revision/latest?cb=20250120145059", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2b/DragonWest233.png/revision/latest?cb=20250211184106", "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f7/DragonWest3489.png/revision/latest?cb=20250211184220", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/eb/DragonEast3422423423.png/revision/latest?cb=20250211184650", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a7/Dragon_150x150.png/revision/latest?cb=20231219002622", "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/4c/Eastern_and_Western_dragon.png/revision/latest?cb=20241218001224", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/94/RedgreenandblueWestern.jpg/revision/latest?cb=20250123095642", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/9d/Dragon_%28Classic%29.png/revision/latest?cb=20220228195629", "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c1/Dragon_%28Classic%29_Fruit.png/revision/latest?cb=20241215120055"], "lastUpdated": {"$date": "2025-07-19T19:24:38.985Z"}, "rawData": {"infobox": {"name": "Dragon", "tab1": "All", "image1": "Dragon Fruit.png Fruit Icon Dragon.png Icon", "tab2": "East", "image2": "Dragon (East) Fruit.png Fruit Icon Dragon (East)Fruit Gif.gif Fruit GIF Dragon (East).png Icon", "tab3": "West", "image3": "Dragon (West) Fruit.png Fruit Icon Dragonwest.gif Fruit GIF Dragon (West).png Icon", "type": "Beast", "rarity": "Mythical", "m1": "Yes", "update": "13", "money": "15,000,000"}, "wikitextLength": 30596, "movesFound": 24, "statsFound": 24, "extractedAt": "2025-07-19T19:24:38.985Z"}, "type": "fruit", "wikiUrl": "https://blox-fruits.fandom.com/wiki/Dragon"}