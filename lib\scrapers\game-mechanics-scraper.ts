import { MongoClient } from "mongodb"
import { BaseScraper } from "./base-scraper"
import { ScrapedItem } from "./types"

interface GameMechanic {
  name: string
  type: "combat" | "leveling" | "trading" | "farming" | "pvp" | "raid"
  description: string
  formula?: string
  parameters?: Record<string, any>
  examples?: string[]
  lastUpdated: Date
}

export class GameMechanicsScraper extends BaseScraper {
  private mongoClient: MongoClient

  constructor(mongoUrl: string) {
    super()
    this.mongoClient = new MongoClient(mongoUrl)
  }

  // Implement abstract methods from BaseScraper
  async scrapeItem(title: string, itemType: string = "mechanic"): Promise<ScrapedItem | null> {
    const content = await this.getPageContent(title)
    if (content) {
      const mechanics = this.extractMechanics(content, "general")
      if (mechanics.length > 0) {
        return {
          _id: { $oid: "" },
          name: mechanics[0].name,
          category: "mechanic",
          mechanicData: mechanics[0] as any,
          imageUrl: "",
          imageUrls: [],
          lastUpdated: new Date(),
          type: "mechanic",
          wikiUrl: `https://blox-fruits.fandom.com/wiki/${title}`
        }
      }
    }
    return null
  }

  extractSpecificData(wikitext: string): any {
    return this.extractMechanics(wikitext, "general")
  }

  private extractMechanics(wikitext: string, type: GameMechanic["type"]): GameMechanic[] {
    const mechanics: GameMechanic[] = []

    // Extraire les sections avec des formules
    const sections = wikitext.split(/==\s*([^=]+)\s*==/g)

    for (let i = 1; i < sections.length; i += 2) {
      const sectionTitle = sections[i].trim()
      const sectionContent = sections[i + 1] || ""

      // Chercher les formules mathématiques
      const formulas = sectionContent.match(/\{\{math\|([^}]+)\}\}/g) || []
      const codeBlocks = sectionContent.match(/\{\{code\|([^}]+)\}\}/g) || []

      if (
        formulas.length > 0 ||
        codeBlocks.length > 0 ||
        sectionContent.includes("formula") ||
        sectionContent.includes("calculation")
      ) {
        const mechanic: GameMechanic = {
          name: sectionTitle,
          type,
          description: this.extractDescription(sectionContent),
          lastUpdated: new Date(),
        }

        // Extraire la formule
        if (formulas.length > 0 && formulas[0]) {
          mechanic.formula = formulas[0].replace(/\{\{math\|([^}]+)\}\}/, "$1")
        }

        // Extraire les paramètres
        const parameters = this.extractParameters(sectionContent)
        if (Object.keys(parameters).length > 0) {
          mechanic.parameters = parameters
        }

        // Extraire les exemples
        const examples = this.extractExamples(sectionContent)
        if (examples.length > 0) {
          mechanic.examples = examples
        }

        mechanics.push(mechanic)
      }
    }

    return mechanics
  }

  private extractDescription(content: string): string {
    // Nettoyer et extraire la description
    const cleaned = content
      .replace(/\{\{[^}]*\}\}/g, "")
      .replace(/\[\[[^\]]*\]\]/g, "")
      .replace(/'''?([^']+)'''?/g, "$1")
      .trim()

    const sentences = cleaned.split(/[.!?]/)
    const description = sentences.slice(0, 3).join(". ").trim()

    return description.length > 20 ? description : cleaned.substring(0, 200)
  }

  private extractParameters(content: string): Record<string, any> {
    const parameters: Record<string, any> = {}

    // Chercher les définitions de variables
    const variablePatterns = [
      /(\w+)\s*=\s*([^,\n]+)/g,
      /where\s+(\w+)\s+is\s+([^,\n]+)/gi,
      /let\s+(\w+)\s*=\s*([^,\n]+)/gi,
    ]

    for (const pattern of variablePatterns) {
      let match
      while ((match = pattern.exec(content)) !== null) {
        const key = match[1].trim()
        const value = match[2].trim()

        if (key && value && key.length < 20) {
          parameters[key] = value
        }
      }
    }

    return parameters
  }

  private extractExamples(content: string): string[] {
    const examples: string[] = []

    // Chercher les exemples
    const examplePatterns = [/example:?\s*([^.\n]+)/gi, /for example:?\s*([^.\n]+)/gi, /e\.g\.?\s*([^.\n]+)/gi]

    for (const pattern of examplePatterns) {
      let match
      while ((match = pattern.exec(content)) !== null) {
        const example = match[1].trim()
        if (example && example.length > 10) {
          examples.push(example)
        }
      }
    }

    return examples
  }

  async scrapeMechanics(): Promise<GameMechanic[]> {
    console.log("🔍 Scraping game mechanics...")

    const mechanicPages = [
      { page: "Combat", type: "combat" as const },
      { page: "Damage Calculation", type: "combat" as const },
      { page: "Leveling", type: "leveling" as const },
      { page: "Experience", type: "leveling" as const },
      { page: "Trading", type: "trading" as const },
      { page: "Farming", type: "farming" as const },
      { page: "PvP", type: "pvp" as const },
      { page: "Raids", type: "raid" as const },
    ]

    const allMechanics: GameMechanic[] = []

    for (const { page, type } of mechanicPages) {
      console.log(`Checking page: ${page}`)

      const content = await this.getPageContent(page)
      if (content) {
        const mechanics = this.extractMechanics(content, type)
        allMechanics.push(...mechanics)
        console.log(`Found ${mechanics.length} mechanics in ${page}`)
      }
    }

    return allMechanics
  }

  async updateDatabase(): Promise<void> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")
      const collection = db.collection("gamemechanics")

      const mechanics = await this.scrapeMechanics()

      if (mechanics.length > 0) {
        // Supprimer les anciennes mécaniques
        await collection.deleteMany({})

        // Insérer les nouvelles mécaniques
        await collection.insertMany(mechanics)

        console.log(`✅ Updated ${mechanics.length} game mechanics in database`)
      } else {
        console.log("⚠️ No game mechanics found")
      }
    } catch (error) {
      console.error("❌ Error updating game mechanics:", error)
    } finally {
      await this.mongoClient.close()
    }
  }
}
