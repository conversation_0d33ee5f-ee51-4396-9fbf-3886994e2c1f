{"_id": {"$oid": "687bf0fc03c56f76ca1e7db0"}, "name": "Gravity", "category": "fruit", "fruitData": {"type": "Natural", "rarity": "Mythical", "introducedUpdate": "5", "m1Capability": true, "awakening": false, "transformation": false, "mainDescription": "Gravity allows the user to control and manipulate the gravitational force in their environment, enabling them to influence many elements on the battlefield. They can create powerful black holes, levitate rocks, manipulate the gravitational force acting on enemies, and pull-down meteors—both large and small—towards the earth. The user can cause island-scale destruction by creating huge meteors and large black holes, even pulling the entire moon down to earth as an attack. Any foes will be easily destroyed by the sheer power of the user.", "combatRating": {"pvp": "Excellent", "grinding": "Excellent"}, "furyMeterMechanics": {"gravitationalForce": {"type": "gravitational", "fillMethod": "damage-based", "description": "gravitational force in their environment, enabling them to influence many elements on the battlefield. They can create powerful black holes, levitate rocks, manipulate the gravitational force acting on enemies, and pull-down meteors—both large and small—towards the earth. The user can cause island-scale destruction by creating huge meteors and large black holes, even pulling the entire moon down to earth as an attack. Any foes will be easily destroyed by the sheer power of the user. Gravity is widely regarded as one of the most powerful and abusable fruits for PvP due to its high damage output, expansive AoE capabilities, very long stuns, extremely long range and largely unavoidable abilities—especially when fully upgraded—making it an popular choice. Several of its skills, particularly and , are very abusable due to their exceptional damage and AoE range, and for , the user is able to become fully invincible to all attacks during the cutscene. As a result, Gravity holds significant trade value, despite being the least expensive among fruits. It offers excellent combo potential and reliable crowd control, making it particularly effective against many Beast-type fruits such as Buddha, Mammoth , Dragon and even teamers. Gravity is a good choice for grinding in all seas, due to the generally useful [M1] abilities, the huge AoE and highly damaging moves. However, it is not recommended for players in the first sea as the abilities all have extremely high mastery and the [M1] isn't so reliable for consistent damage. Gravity is excellent for all types of activities, from Sea Events to Raids, and even fighting generally shifty enemies such as the Tyrant of the Skies and Order, due to its huge and long stuns that can hold enemies in one place. Gravity can also be used to eliminate dangerous sea events quickly such as Piranhas and Terrorsharks, though the cannot hit water-borne enemies in any way. __TOC__ Gravity can be upgraded via the Admin Panel: !Ability !Mastery !Research !Changes !Cost", "requirements": []}, "powerMeter": {"type": "power", "fillMethod": "time-and-damage-based", "description": "Power Meter meter]] that is not intended for a transformation. * Gravity is the only fruit in the game that requires the most tasks and items to fully upgrade it through the Admin Panel. * Gravity is the second fruit to have a cutscene, the first being Control. * Gravity currently has the longest cutscene of any fruit, being roughly 13.79 seconds. * Gravity has been reworked two times and has two moves that are uncancellable. * When going through doorways while flying, the boulder will purposely lower into the ground to allow the user to enter. * 's upgraded version is very similar to an admin ability called \"Tensei\". * The users are unable to use the max upgraded while flying with Gravity. However, it still works even if they stop without ending the flight mode. * Removing the stage 2 [M1] shortens the time to reach stage 3, however removing the stage 1 [M1] causes the stage 3 animation to play for a short time before becoming normal. * A physical Gravity fruit can be dropped from defeating the Tyrant of the Skies, at a very low chance. * Gravity and Blade are the only fruits that have had their entire moveset reworked twice. * Gravity's [M1] has an alternative button on mobile located above [Z] Singularity that functions just the same as tapping, although resembles the activation of a move instead. * Unlike other M1 attacks on mobile, 2 clicks are required to activate the [M1] ability. == * has a glitch when if flown onto the ground (in which it is stuck) can cause the move to automatically end. * Gravity’s “Defensive Rift” Passive can be stacked permanently, meaning the player can deal an infinite amount of damage and one shot anything if done at close range once 20% damage is received. (This is likely to be patched quickly.) * Sometimes, when using a move while flying, the user will start to ragdoll. * In rare cases, while using in water/sea, the user instantly dies due to an unknown glitch. (It is recommended to use while staying out of water.) * If the user gets teleported to a raid island with the use of the , they get flinged around the map and do not get teleported to the raid thus wasting the money/fruit. * If the user uses other Gravity skills and then uses the tap version of , there is a chance that when the circle of explodes, a negative colors circle can appear, and will disappear after 1-2 seconds. * Randomly, the held variant of [V] Asteroid Crash will not work and the player will have to talk to the Mysterious Scientist in order to unequip and reequip the ability to use it again. * If the user misses the held variation of [V] Asteroid Crash, their hands will have a glowing effect until they die or leave the game. This can also stack. * If the user is too high (about 300 meters) when they use [V] Asteroid Crash, sometimes the move will not trigger but will still go on cooldown. * Gravity's [M1] doesn't work on controller. * Before entering the rift that unlocks Draco Race V4, hold [F] Shooting Star and enter the rift. From the moment user enter the rift until the end or failure of the trial, the player will remain in the flying state. Players will not cancel a flight when delivering a specific item. * Gravity was remade available to purchase. * Gravity Fruit's model was slightly remade again. ** The color of the fruit model became darker. ** The pink ring now rotates around the fruit. * Gravity Fruit's icon was remade. * Added [M1] attacks. * All moves were reworked. ** [Z] Gravity Push was renamed to [Z] Singularity. ** [X] Gravity Obeisance was renamed to [X] Orbital Chain. ** [C] Meteor Pitch was renamed to [C] Gravitational Prison. ** [V] Meteors Rain was renamed to [V] Asteroid Crash. ** [F] Boulder Flight was renamed to [F] Shooting Star. *: GravityIconOld.png Previous Icon Old Gravity.png Previous Inventory Icon Gravity rework hd.gif Old Gravity Fruit's model. GravZ.gif Gravity Push (Old) GravX.gif Gravity Obeisance (Old) GravC.gif Meteor Pitch (Old) GravV.gif Meteors Rain (Old) GravF.gif Boulder Flight (Old) ---- * Gravity was made unavailable to purchase from the Blox Fruit Dealer's stock and Shop. ---- * Gravity Fruit's model was remade. : Gravity 150x150.png Old Gravity Fruit's icon. ---- * Gravity was reworked. ** Every ability got a VFX rework. ** Old: [V] Meteors Rain could not be aimed and used to hit randomly. ** New: [V] Meteors Rain can be aimed. ---- * Made Gravity slightly more common. ---- * Gravity was released. }}", "requirements": []}}, "damageResistance": null, "skinSystem": {"defaultSkin": "Green", "craftableSkins": [], "chromaticSkins": [], "acquisition": {}}, "variantsComplete": null, "economicData": {"acquisitionMethods": [{"source": "Blox Fruit Dealer"}, {"source": "Shop"}, {"source": "Sea Event", "location": "can hold enemies in one place", "type": "Sea Event"}], "reworkHistory": {"majorUpdate": "Update 26"}, "competitiveRanking": {"difficulty": "High", "grinding": "Excellent", "pvp": "Excellent"}, "marketAnalysis": {"pricePosition": "Cheapest Mythical fruit", "availability": "Very rare", "tradeValue": "Very High"}}, "changeHistoryAdvanced": [{"update": "26", "changes": [{"description": "Gravity was remade available to purchase.", "type": "general"}, {"description": "Gravity Fruit's model was slightly remade again.", "type": "general"}, {"description": "* The color of the fruit model became darker.", "type": "general"}, {"description": "* The pink ring now rotates around the fruit.", "type": "general"}, {"description": "Gravity Fruit's icon was remade.", "type": "general"}, {"description": "Added [M1] attacks.", "type": "addition"}, {"description": "All moves were reworked.", "type": "general"}, {"description": "* [Z] Gravity Push was renamed to [Z] Singularity.", "type": "general"}, {"description": "* [X] Gravity Obeisance was renamed to [X] Orbital Chain.", "type": "general"}, {"description": "* [C] Meteor Pitch was renamed to [C] Gravitational Prison.", "type": "general"}, {"description": "* [V] Meteors Rain was renamed to [V] Asteroid Crash.", "type": "general"}, {"description": "* [F] Boulder Flight was renamed to [F] Shooting Star.", "type": "general"}, {"description": ":", "type": "general"}], "type": "major_rework"}, {"update": "25", "changes": [{"description": "Gravity was made unavailable to purchase from the Blox Fruit Dealer's stock and Shop.", "type": "general"}], "type": "general"}, {"update": "20", "changes": [{"description": "Gravity Fruit's model was remade.", "type": "general"}], "type": "general"}, {"update": "17.3", "changes": [{"description": "Gravity was reworked.", "type": "general"}, {"description": "* Every ability got a VFX rework.", "type": "general"}, {"description": "* Old: [V] Meteors Rain could not be aimed and used to hit randomly.", "type": "general"}, {"description": "* New: [V] Meteors Rain can be aimed.", "type": "general"}], "type": "major_rework"}, {"update": "7", "changes": [{"description": "Made Gravity slightly more common.", "type": "general"}], "type": "general"}], "forms": [{"name": "Moveset", "type": "Normal", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "This move possesses three variations:\n• The user summons a small gravitational vortex at their cursor with a limited range, which explodes shortly after, dealing minor damage. Upgraded version has higher damage and a wider radius.\n• The user makes a swiping motion with their hand, creating a large gravitational sweep to the left, right, up and down. Upgraded version has higher damage and very high knockback.\n• The user summons a meteor from beyond the skies, landing at the location of their cursor, dealing high damage. Upgraded version summons more meteors, dealing higher damage.", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/95/GravM1T.gif/revision/latest?cb=20250601223926", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c3/GravM1S.gif/revision/latest?cb=20250601224104", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2c/GravM1M.gif/revision/latest?cb=20250601224215"}, {"key": "Z", "name": "Singularity", "description": "The user creates a singularity within their hand, distorting spacetime, pulling in and stunning enemies whilst simultaneously distorting both parties' screens. The singularity then explodes in an AoE blast, knocking the enemies back.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b2/RGravZ.gif/revision/latest?cb=20250601225214", "gif1": null, "gif2": null, "effects": ["stun", "aoe"]}, {"key": "X", "name": "Orbital Chain", "description": "The user creates a black hole right above them, which picks up and spews out countless debris in the direction of their cursor, dealing minor AoE damage to enemies. This move can be held down forever, however, after a few seconds the speed debris that strikes begins to slow down, causing both the damage and stun to weaken.", "mastery": 100, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/RGravX.gif/revision/latest?cb=20250601225759", "gif1": null, "gif2": null, "effects": ["stun", "aoe"]}, {"key": "C", "name": "Gravitational Prison", "description": "This move possesses two distinct variations: The specific variations depend on whether the move is tapped or held, each providing different effects and damage patterns.", "mastery": 200, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/GravityC2.gif/revision/latest?cb=20250601225308", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/05/UpgradedGravityC.gif/revision/latest?cb=20250425081843", "gif2": null}, {"key": "V", "name": "Asteroid Crash", "description": "This move possesses two distinct variations: The specific variations depend on whether the move is tapped or held, each providing different effects and damage patterns.", "mastery": 300, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/13/GravVH.gif/revision/latest?cb=20250601114738", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8d/GravVT.gif/revision/latest?cb=20250420041010", "gif2": null}, {"key": "F", "name": "Shooting Star", "description": "The user transforms into a meteor and launches themselves forward, dealing damage to enemies in their path. This move can be used for both offense and mobility.", "mastery": 50, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fb/GravFT.gif/revision/latest?cb=20250420041447", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/90/GravFHG.gif/revision/latest?cb=20250601225606", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/96/GravFHA.gif/revision/latest?cb=20250420041636"}]}], "passiveAbilities": [], "masteryRequirements": {"Normal Attack": 0, "Singularity": 1, "Orbital Chain": 100, "Gravitational Prison": 200, "Asteroid Crash": 300, "Shooting Star": 50}, "priceData": {"current": {"money": 2500000, "status": "AVAILABLE", "robux": 2300}, "sources": ["Blox Fruit Dealer", "Shop"], "historical": [{"money": 2300, "context": "Robux price", "type": "robux"}]}, "trivia": ["Gravity is the cheapest fruit in the game.", "The second rework concept of the Gravity moves was created by concept artist @crazy_rtx.", "Gravity is the only fruit in the game to have 3 variants of its [M1] attacks.", "As of now, Gravity is the only fruit with 13 abilities, making it the fruit with the highest number of skills in the game (including [M1] clicks).", "The boss <PERSON><PERSON><PERSON> uses a slightly altered version of Gra<PERSON>, though the style of the moves resemble the old one.", "Gravity is one of the first two fruits that could obtain upgrades through the Admin Panel, the other being Eagle and soon, Creation.", "Gravity is one of six fruits to have a meter that is not intended for a transformation.", "Gravity is the only fruit in the game that requires the most tasks and items to fully upgrade it through the Admin Panel.", "Gravity is the second fruit to have a cutscene, the first being Control.", "Gravity currently has the longest cutscene of any fruit, being roughly 13.79 seconds.", "Gravity has been reworked two times and has two moves that are uncancellable.", "When going through doorways while flying, the boulder will purposely lower into the ground to allow the user to enter.", "'s upgraded version is very similar to an admin ability called \"Tensei\".", "The users are unable to use the max upgraded while flying with Gravity. However, it still works even if they stop without ending the flight mode.", "Removing the stage 2 [M1] shortens the time to reach stage 3, however removing the stage 1 [M1] causes the stage 3 animation to play for a short time before becoming normal.", "A physical Gravity fruit can be dropped from defeating the Ty<PERSON> of the Skies, at a very low chance.", "Gravity and Blade are the only fruits that have had their entire moveset reworked twice.", "Gravity's [M1] has an alternative button on mobile located above [Z] Singularity that functions just the same as tapping, although resembles the activation of a move instead.", "Unlike other M1 attacks on mobile, 2 clicks are required to activate the [M1] ability."], "gallery": [{"url": "latest", "caption": "Fruit Icon"}, {"url": "latest", "caption": "Fruit GIF"}, {"url": "latest", "caption": "Icon"}, {"url": "latest", "caption": "Concept art of the [V] move \"The Planet\" posted on Twitter by @disardo1."}, {"url": "latest", "caption": "Shop artwork for the Gravity Fruit. Made by @EGOTISMS_RBLX."}, {"url": "latest", "caption": "Previous Icon"}, {"url": "latest", "caption": "Previous Inventory Icon"}, {"url": "latest", "caption": "Old Gravity Fruit's model."}, {"url": "latest", "caption": "Gravity Push (Old)"}, {"url": "latest", "caption": "Gravity Obeisance (Old)"}, {"url": "latest", "caption": "<PERSON><PERSON> (Old)"}, {"url": "latest", "caption": "Meteors Rain (Old)"}, {"url": "latest", "caption": "Boulder Flight (Old)"}, {"url": "latest", "caption": "Old Gravity Fruit's icon."}], "shopQuote": "Allows the user to manipulate gravity, controlling the battlefield.", "upgradeData": {"Kinetic Amplifier": {"mastery": 200, "research": "Defeat 15 enemies with the [M1] ability of Gravity. It is recommended to defeat Bandits at Pirate Starter or Trainees at Marine Starter in the First Sea due to their low health.", "changes": "Upgrades [M1] ability, allowing the player to hold their [M1] for more damage and range.", "cost": [{"item": "Fragments", "amount": 1000}, {"item": "Radioactive Material", "amount": 2}, {"item": "Mystic Droplet", "amount": 1}]}, "Kinetic Amplifier 2": {"mastery": 300, "research": "Defeat 30 enemies with the [M1] ability of Gravity. It is recommended to defeat Bandits at Pirate Starter or Trainees at Marine Starter in the First Sea due to their low health.", "changes": "Further upgrades [M1] ability, allowing the player to hold their [M1] for even more damage and range.", "cost": [{"item": "Fragments", "amount": 2000}, {"item": "Radioactive Material", "amount": 4}, {"item": "Mystic Droplet", "amount": 2}]}, "Dark Matter Implosion": {"mastery": 400, "research": "Use Gravity skills on ships 20 times.", "changes": "Grants a Gravitational Force Meter, as well as upgrading the [C] ability, changing its functionality and dealing more damage when held at full Gravitational Force Meter.", "cost": [{"item": "Fragments", "amount": 6000}, {"item": "Meteorite", "amount": 1}, {"item": "Radioactive Material", "amount": 6}, {"item": "Mystic Droplet", "amount": 4}]}}, "instinctData": {"Tap": {"name": "Normal Attack", "canDodge": true, "breaksInstinct": false, "notes": ""}, "Z": {"name": "Singularity", "canDodge": false, "breaksInstinct": true, "notes": "Only the pull when held can be dodged. The explosion breaks Instinct."}, "X": {"name": "Orbital Chain", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "C": {"name": "Gravitational Prison (Held)", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "V": {"name": "Asteroid Crash (Held)", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "F": {"name": "Shooting Star", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}}, "formSpecific": {"General": {"pros": ["Insane damage.", "High trade value.", "Great combo potential.", "All moves have a huge hitbox.", "All moves break Instinct.", "Can be good for grinding"], "cons": ["Weak in air.", "Cannot catch up with fast fruits/swords users. (for example: Light, Portal, etc.)", "Moves have relatively longer start-up than other PvP fruits, making it easier for enemies to dodge the attacks.", "Requires a certain amount of prediction.", "Passive teleportation completely overrides Draco and Human V4 flash step.", "Has some end-lag.", "High mastery requirement. (Even higher to unlock the upgrades)"]}}}, "imageUrl": "Gravity Fruit.png", "imageUrls": ["https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b5/GravitationalStepsofForce.gif/revision/latest?cb=20250418174108", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/ae/GravitationalForce.png/revision/latest?cb=20250419082355", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/95/GravM1T.gif/revision/latest?cb=20250601223926", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b2/RGravZ.gif/revision/latest?cb=20250601225214", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/ec/GravityDefense.gif/revision/latest?cb=20250425060333", "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5f/Gravity_Fruit.png/revision/latest?cb=20250418030958", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/GravityC2.gif/revision/latest?cb=20250601225308", "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/RGravX.gif/revision/latest?cb=20250601225759", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8d/GravVT.gif/revision/latest?cb=20250420041010", "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fb/GravFT.gif/revision/latest?cb=20250420041447", "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/Gravity.png/revision/latest?cb=20250418030954", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/31/GravityFruitReworkedGif.gif/revision/latest?cb=20250418132752", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/92/GravityWorldConceptArt.png/revision/latest?cb=20250426041021", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7c/GravityBanner.png/revision/latest?cb=20250516093105", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6d/GravityIconOld.png/revision/latest?cb=20250523083109", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/91/GravZ.gif/revision/latest?cb=20230620061732", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7e/GravX.gif/revision/latest?cb=20230620061907", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/be/GravC.gif/revision/latest?cb=20230620061959", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6a/Gravity_rework_hd.gif/revision/latest?cb=20250120174648", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/31/Old_Gravity.png/revision/latest?cb=20250420045334", "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/06/Gravity_150x150.png/revision/latest?cb=20231219002643", "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/54/GravF.gif/revision/latest?cb=20230620062051", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2c/GravV.gif/revision/latest?cb=20230620150556"], "lastUpdated": {"$date": "2025-07-19T19:24:44.805Z"}, "rawData": {"infobox": {"name": "Gravity", "image": "Gravity Fruit.png Fruit Icon GravityFruitReworkedGif.gif Fruit GIF Gravity.png Icon", "type": "Natural", "rarity": "Mythical", "m1": "Yes", "update": "5", "money": "2,500,000"}, "wikitextLength": 23756, "movesFound": 6, "statsFound": 6, "extractedAt": "2025-07-19T19:24:44.805Z"}, "type": "fruit", "wikiUrl": "https://blox-fruits.fandom.com/wiki/Gravity"}